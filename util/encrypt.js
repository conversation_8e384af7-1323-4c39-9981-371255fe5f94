import crypto from 'crypto'

/**
 * sha1加密
 * @param str
 * @returns {*|Hmac.digest}
 */
export function hashSha1 (str) {
  return crypto.createHash('sha1').update(str).digest('hex')
}

/**
 * MD5
 * @param str
 * @returns {*|Hmac.digest}
 */
export function md5 (str) {
  return crypto.createHash('md5').update(str, 'utf8').digest('hex')
}

/**
 * 加密
 * @param str
 * @param secret
 * @returns {*}
 */
export function encrypt (str, secret) {
  // const cipher = crypto.createCipher('aes192', secret)
  const cipher = crypto.createCipheriv('aes192', secret, null)
  let enc = cipher.update(str, 'utf8', 'hex')
  enc += cipher.final('hex')
  return enc
}

/**
 * 解密
 * @param str
 * @param secret
 * @returns {*}
 */
export function decrypt (str, secret) {
  // const decipher = crypto.createDecipher('aes192', secret)
  const decipher = crypto.createDecipheriv('aes192', secret, null)
  let dec = decipher.update(str, 'hex', 'utf8')
  dec += decipher.final('utf8')
  return dec
}
