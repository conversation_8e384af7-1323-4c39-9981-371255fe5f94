import DataLoader from 'dataloader'
import _ from 'lodash-es'
import config from 'config'
import kn from './kn.js'
const illegalImg = config.get('illegalImg')
const imgHost = config.get('imgHost')

function genLoader (tableName, columnName = 'id', columns = null) {
  return new DataLoader(async function (ids) {
    const qb = kn(tableName).whereIn(columnName, ids)
    const list = columns ? await qb.select(columns) : await qb.select()
    const indexed = _.keyBy(list, columnName)
    return ids.map(i => indexed[i] || null)
  })
}
export default function create (account) {
  return {
    userLoader: genLoader('user', 'id', ['id', 'app', 'name', 'gender', 'avatar', 'mobile', 'classCode']),
    userProfilesLoader: new DataLoader(async function (userIds) {
      const list = await kn('user_profile').whereIn('userId', userIds).select()
      const grouped = _.reduce(list, (memo, item) => {
        memo[item.userId] = memo[item.userId] || []
        memo[item.userId].push(item)
        return memo
      }, {})
      return userIds.map(i => grouped[i] || [])
    }),
    profileLoader: genLoader('user_profile', 'id', ['id', 'self', 'name', 'avatar', 'gender']),
    statusLoader: genLoader('status'),
    statusImagesLoader: new DataLoader(async function (statusIds) {
      const list = await kn.table('status_image').whereIn('statusId', statusIds).select()
      const grouped = _.reduce(list, (memo, item) => {
        memo[item.statusId] = memo[item.statusId] || []
        let img = item.illegal ? illegalImg : item.path
        if (!item.path.startsWith('http')) img = imgHost + img
        memo[item.statusId].push(img)
        return memo
      }, {})
      return statusIds.map(i => grouped[i] || [])
    }),
    statusWeightLoader: genLoader('status_weight', 'statusId'),
    statusSportLoader: genLoader('status_sport', 'statusId'),
    statusMealLoader: genLoader('status_meal', 'statusId'),
    statusMeasurementLoader: genLoader('status_measurement', 'statusId'),
    statusPressureLoader: genLoader('status_pressure', 'statusId'),
    statusGlucoseLoader: genLoader('status_glucose', 'statusId'),
    statusLipidLoader: genLoader('status_lipid', 'statusId'),
    statusAcidLoader: genLoader('status_acid', 'statusId'),
    statusLikeLoader: new DataLoader(async function (statusIds) {
      if (!account) return statusIds.map(() => false)
      const list = await kn.table('like').whereIn('userId', account.mpUsers.map(i => i.id)).where({ entityType: 'status', deleted: 0 }).whereIn('entityId', statusIds).select('entityId')
      const indexed = _.keyBy(list, 'entityId')
      return statusIds.map(i => !!indexed[i])
    }),
    commentLoader: new DataLoader(async function (ids) {
      let list = await kn.table('comment').whereIn('id', ids).select()
      list = list.map(i => i.deleted ? { ...i, content: '已删除' } : i.illegal ? { ...i, content: '内容违规' } : i)
      const indexed = _.keyBy(list, 'id')
      return ids.map(i => indexed[i])
    }),
    commentLikeLoader: new DataLoader(async function (statusIds) {
      if (!account) return statusIds.map(() => false)
      const list = await kn.table('like').whereIn('userId', account.mpUsers.map(i => i.id)).where({ entityType: 'comment', deleted: 0 }).whereIn('entityId', statusIds).select('entityId')
      const indexed = _.keyBy(list, 'entityId')
      return statusIds.map(i => !!indexed[i])
    }),
    userFollowedLoader: new DataLoader(async function (userIds) {
      // 0 未关注 1 关注 2 相互关注 3 他关注我
      if (!account) return userIds.map(() => 0)
      const list = await kn('user_follower').where(qb => {
        qb.where('followerUserId', account.id).whereIn('userId', userIds)
      }).orWhere(qb => {
        qb.where('userId', account.id).whereIn('followerUserId', userIds)
      }).select('id', 'userId', 'followerUserId')
      const indexed = _.reduce(list, (memo, item) => {
        return !memo[item.followerUserId]
          ? { [item.followerUserId]: { [item.userId]: true } }
          : { ...memo, [item.followerUserId]: { ...memo[item.followerUserId], [item.userId]: true } }
      }, {})
      return userIds.map(id => {
        const following = indexed[account.id] && indexed[account.id][id]
        const followedBy = indexed[id] && indexed[id][account.id]
        return following && followedBy ? 2 : following ? 1 : followedBy ? 3 : 0
      })
    }),
    productLoader: genLoader('product'),
    likeLoader: genLoader('like'),
    orderProductsLoader: new DataLoader(async function (orderIds) {
      const list = await kn.table('order_product').whereIn('orderId', orderIds).select()
      const grouped = _.reduce(list, (memo, item) => {
        memo[item.orderId] = memo[item.orderId] || []
        memo[item.orderId].push(item)
        return memo
      }, {})
      return orderIds.map(i => grouped[i])
    }),
    orderRefundLoader: genLoader('order_refund'),
    orderRefundsLoader: new DataLoader(async function (orderIds) {
      const list = await kn.table('order_refund').whereIn('orderId', orderIds).select()
      const grouped = _.reduce(list, (memo, item) => {
        memo[item.orderId] = memo[item.orderId] || []
        memo[item.orderId].push(item)
        return memo
      }, {})
      return orderIds.map(i => grouped[i])
    }),
    orderCommissionLoader: genLoader('order_commission'),
    orderCommissionsLoader: new DataLoader(async function (orderIds) {
      const list = await kn.table('order_commission').whereIn('orderId', orderIds).select()
      const grouped = _.reduce(list, (memo, item) => {
        memo[item.orderId] = memo[item.orderId] || []
        memo[item.orderId].push(item)
        return memo
      }, {})
      return orderIds.map(i => grouped[i])
    }),
    orderShippingsLoader: new DataLoader(async function (orderIds) {
      const list = await kn.table('order_shipping').whereIn('orderId', orderIds).select()
      const grouped = _.reduce(list, (memo, item) => {
        memo[item.orderId] = memo[item.orderId] || []
        memo[item.orderId].push(item)
        return memo
      }, {})
      return orderIds.map(i => grouped[i] || [])
    }),
    // 为了在不同的 query 不重复查询而建立的 loader
    progressingPlanLoader: new DataLoader(async function (profileIds) {
      const results = []
      for (const profileId of profileIds) {
        if (!account) return null
        const item = await kn.table('plan').where({ userId: account.id, profileId, status: 1 }).first()
        if (item) {
          results.push(item)
        } else {
          const item2 = await kn.table('plan').where({ userId: account.id, status: 0 }).orderBy('id', 'asc').first()
          results.push(item2 ?? null)
        }
      }
      return results
    }),
    courseLoader: genLoader('course'),
    courseSectionsLoader: new DataLoader(async function (courseIds) {
      const list = await kn.table('course_section').whereIn('courseId', courseIds).orderBy('sectionIndex', 'asc').select()
      const grouped = _.reduce(list, (memo, item) => {
        memo[item.courseId] = memo[item.courseId] || []
        memo[item.courseId].push(item)
        return memo
      }, {})
      return courseIds.map(i => grouped[i] || [])
    }),
    courseSectionLoader: genLoader('course_section')
  }
}
