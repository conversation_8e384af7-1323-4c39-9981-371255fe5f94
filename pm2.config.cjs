const production = process.env.NODE_ENV === 'production'
module.exports = {
  apps: [
    {
      name: 'tqq-2024-admin-api',
      script: './server.js',
      watch: !production,
      ignore_watch: ['node_modules', '*.log', '.txt', 'logs'],
      exec_mode: 'fork',
      instances: 1,
      instance_var: 'tqq-2024-admin-api',
      log_date_format: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      name: 'tqq-2024-admin-task',
      script: './task/index.js',
      watch: false,
      ignore_watch: ['node_modules', '*.log', '.txt', 'logs'],
      exec_mode: 'fork',
      instance_var: 'tqq-2024-admin-task',
      log_date_format: 'YYYY-MM-DD HH:mm:ss',
      merge_logs: true,
      log_file: '/root/.pm2/logs/tqq-2024-admin-task-combined.log'
    }
  ],
  deploy: {
    production: {
      host: ['aliyun'],
      ref: 'origin/main',
      repo: '**************:ynf/tqq-2024-admin-api.git',
      path: '/root/sites/tqq2024/admin-api',
      ssh_options: 'StrictHostKeyChecking=no',
      'pre-deploy-local': 'export NODE_ENV=production',
      'pre-deploy': "echo '开始拉取'",
      'post-deploy': 'export NODE_ENV=production && pm2 reload pm2.config.cjs'
    }
  }
}
