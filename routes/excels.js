import express from 'express'
import multer from 'multer'
import * as xlsx from 'xlsx'
import kn from '../db/kn.js'
const router = express.Router()

// 配置 multer 存储
const storage = multer.memoryStorage()
const upload = multer({ storage })

router.post('/outer-orders', upload.single('file'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: '没有上传文件' })
  }

  try {
    // 读取 Excel 文件
    const workbook = xlsx.read(req.file.buffer, { type: 'buffer' })
    const sheetName = workbook.SheetNames[0]
    const sheet = workbook.Sheets[sheetName]
    // 将 Excel 数据转换为 JSON
    const jsonData = xlsx.utils.sheet_to_json(sheet)
    if (jsonData.length === 0) return res.status(400).json({ message: 'Excel 文件中没有数据' })
    const outerApp = jsonData[0]['达人抖音号'] ? '抖音' : ''
    if (!outerApp) return res.status(400).json({ message: '现在只能导入抖音订单' })
    const data = jsonData.map(i => ({
      outerApp,
      outerOrderNumber: i['订单id'],
      outerOrderStatus: i['订单状态'],
      outerProductId: i['商品id'],
      outerPayTime: i['订单支付时间']
    }))
    for (const item of data) {
      await kn('shen_outer_order')
        .insert(item)
        .onConflict(['outerApp', 'outerOrderNumber'])
        .merge({
          outerOrderStatus: item.outerOrderStatus,
          outerProductId: item.outerProductId,
          outerPayTime: item.outerPayTime
        })
    }
    res.status(200).json({ message: '已上传' })
  } catch (error) {
    console.error('处理 Excel 文件时出错:', error)
    res.status(500).json({ message: '处理 Excel 文件时出错' })
  }
})

export default router
