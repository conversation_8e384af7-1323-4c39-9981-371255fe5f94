import express from 'express'
import config from 'config'
// import kn from '../db/kn.js'
// import { getTicket, getAgentTicket } from '../helper/qywx/ticket-config.js'
// import { hashSha1 } from '../util/encrypt.js'
import { getSignature, decrypt } from '@wecom/crypto'
import xml2js from 'xml2js'
import { sendWelcomeMessage } from '../helper/qywx/send-messge.js'

const router = express.Router()
// const { corpId, agentId } = config.get('qywx')
// const createNonceStr = () => Math.random().toString(36).substr(2, 15)
// const createTimestamp = () => Math.floor(new Date().getTime() / 1000) + ''

// router.get('/config', (req, res, next) => {
//   getTicket(function (err, ticket) {
//     if (err) return next(err)
//     const url = req.get('referer')
//     const nonceStr = createNonceStr()
//     const timestamp = createTimestamp()
//     const str = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`
//     const signature = hashSha1(str)
//     const model = {
//       beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
//       debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
//       appId: corpId, // 必填，企业微信的corpID
//       timestamp, // 必填，生成签名的时间戳
//       nonceStr, // 必填，生成签名的随机串
//       signature, // 必填，签名，见 附录-JS-SDK使用权限签名算法
//       jsApiList: [
//         'openUserProfile',
//         'scanQRCode',
//         'selectEnterpriseContact',
//         'openEnterpriseChat',
//         'chooseInvoice',
//         'selectExternalContact',
//         'getCurExternalContact',
//         'openUserProfile',
//         'hideAllNonBaseMenuItem'
//       ] // 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
//     }
//     res.json(model)
//   })
// })

// router.get('/agent_config', (req, res, next) => {
//   getAgentTicket(function (err, ticket) {
//     if (err) return next(err)
//     const url = req.get('referer')
//     const nonceStr = createNonceStr()
//     const timestamp = createTimestamp()
//     const str = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`
//     const signature = hashSha1(str)
//     const model = {
//       corpid: corpId, // 必填，企业微信的corpid，必须与当前登录的企业一致
//       agentid: agentId, // 必填，企业微信的应用id
//       timestamp, // 必填，生成签名的时间戳
//       nonceStr, // 必填，生成签名的随机串
//       signature, // 必填，签名，见附录1
//       jsApiList: [
//         'selectExternalContact',
//         'openUserProfile',
//         'shareToExternalContact',
//         'sendChatMessage',
//         'getCurExternalContact',
//         'getCurExternalChat',
//         'applyCodeForCreateChat'
//       ] // 必填
//     }
//     res.json(model)
//   })
// })
router.get('/external_contact_callback', (req, res, next) => {
  console.log('get external_contact_callback ')
  console.log(req.query)
  try {
    const { msg_signature: msgSignature, timestamp, nonce, echostr } = req.query
    if (!msgSignature || !timestamp || !nonce || !echostr) {
      console.log('no query data')
      res.send('error')
      return
    }
    const { token, encodingAESKey } = config.get('qywx').get('extContactSecret')
    const sign = getSignature(token, timestamp, nonce, echostr)
    console.log(sign)
    if (sign === msgSignature) {
      const msg = decrypt(encodingAESKey, echostr)
      console.log(msg, typeof msg)
      res.send(msg.message)
    } else {
      res.send('error')
    }
    // const resStr = externalCrypt.verifyURL(req.query.msg_signature, req.query.timestamp, req.query.nonce, req.query.echostr)
    // logger.debug(resStr)
    // res.send(resStr)
  } catch (e) {
    res.send('error')
  }
})
router.post('/external_contact_callback', async (req, res, next) => {
  console.log('post/external_contact_callback')
  try {
    const { msg_signature: msgSignature, timestamp, nonce } = req.query
    if (!msgSignature || !timestamp || !nonce) {
      res.send('error')
      return
    }
    const obj = await xml2js.parseStringPromise(req.body, { explicitArray: false })
    const { token, encodingAESKey } = config.get('qywx').get('extContactSecret')
    const sign = getSignature(token, timestamp, nonce, obj.xml.Encrypt)
    console.log(`msgSignature: ${msgSignature}, timestamp: ${timestamp}, nonce: ${nonce}, token: ${token}, encodingAESKey:${encodingAESKey}, sign: ${sign}`)
    if (sign !== msgSignature) {
      console.log('sign !== ')
      console.log(obj)
      res.send('error')
      return
    }
    // const info = decrypt(encodingAESKey, obj.xml.Encrypt)
    // console.log(info)
    const msg = decrypt(encodingAESKey, obj.xml.Encrypt)
    console.log(msg.message)
    const info = await xml2js.parseStringPromise(msg.message, { explicitArray: false })
    if (info.xml.Event === 'change_external_contact') {
      // 配置了客户联系功能的成员添加外部联系人时 -- 外部联系人添加了配置了客户联系功能且开启了免验证的成员时（此时成员尚未确认添加对方为好友），回调该事件
      if (info.xml.ChangeType === 'add_external_contact' || info.xml.ChangeType === 'add_half_external_contact') {
        if (info.xml.WelcomeCode) {
          // 企业微信的 userId
          const userId = info.xml.UserID
          // 企业微信中对应的客户 id
          const externalUserID = info.xml.ExternalUserID
          sendWelcomeMessage(info.xml.WelcomeCode, userId, externalUserID)
            .catch(err => {
              console.error(err)
            })
        }
      }
    }
    res.send('')
  } catch (error) {
    console.error(error)
    res.send('error')
  }

  // let buf = ''
  // req.setEncoding('utf8')
  // req.on('data', function (chunk) { buf += chunk })
  // req.on('end', function () {
  //   const str = externalCrypt.decryptMsg(req.query.msg_signature, req.query.timestamp, req.query.nonce, buf)
  //   logger.info(str)
  //   const obj = x2o(str).xml
  //   const event = obj.Event
  //   if (event === 'change_external_chat') {
  //     // 企业群信息更新
  //     // model.updateGroupChat(obj.ChatId, function (err) {
  //     //   if (err) next(err)
  //     //   res.send('')
  //     // })
  //   } else if (event === 'change_external_contact') {
  //     const { ChangeType: changeType, UserID: userId, ExternalUserID: externalUserId } = obj
  //     // if (changeType === 'edit_external_contact' || changeType === 'add_external_contact' || changeType === 'add_half_external_contact') {
  //     //   model.upsertUserExContact(userId, externalUserId, (err) => {
  //     //     if (err) return next(err)
  //     //     res.send('')
  //     //   })
  //     // } else if (changeType === 'del_external_contact') {
  //     //   model.deleteUserExContact(userId, externalUserId, (err) => {
  //     //     if (err) return next(err)
  //     //     res.send('')
  //     //   })
  //     // } else {
  //     //   res.send('')
  //     // }
  //   } else {
  //     return res.send('')
  //   }
  //   // if (obj.Event === 'change_external_contact') {
  //   //   if (obj.ChangeType === 'add_external_contact') {
  //   //   } else if (obj.ChangeType === 'del_external_contact')
  //   // }
  //   // const { xml: { Event, ChangeType, UserID, ExternalUserID }}
  //   //  {"xml":{"ToUserName":"ww11c4a4452a4bcbff","FromUserName":"sys","CreateTime":"1583410126","MsgType":"event","Event":"change_external_contact","ChangeType":"add_external_contact","UserID":"TaoXiaoLei","ExternalUserID":"wmUIXXCQAAKuzixK4FYyykh3X_CFTf_g","WelcomeCode":"ttMXShQ0PvIfHdSOt5gbU3dkXK9Sk5jMvY9dD-G-i00"}}
  //   //  {"xml":{"ToUserName":"ww11c4a4452a4bcbff","FromUserName":"sys","CreateTime":"1583410096","MsgType":"event","Event":"change_external_contact","ChangeType":"del_external_contact","UserID":"TaoXiaoLei","ExternalUserID":"wmUIXXCQAAKuzixK4FYyykh3X_CFTf_g"}}
  // })
})

export default router
