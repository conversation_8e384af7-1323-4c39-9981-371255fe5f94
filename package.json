{"name": "tqq-admin-api-2024", "version": "1.0.0", "description": "", "type": "module", "scripts": {"dev": "pm2 start pm2.config.cjs --only tqq-2024-admin-api", "deploy-production-setup": "pm2 deploy pm2.config.cjs production setup", "deploy-production": "pm2 deploy pm2.config.cjs production", "test": "echo \"Error: no test specified\" && exit 1"}, "graphql": {"schema": "graphql/schema.graphql", "documents": "./graphql/*.{graphql,js,ts,jsx,tsx}"}, "author": "", "license": "ISC", "dependencies": {"@apollo/server": "^4.11.0", "@wecom/crypto": "^1.0.1", "axios": "^1.7.4", "config": "^3.3.12", "connect-redis": "^7.1.1", "cron": "^3.1.7", "dataloader": "^2.2.2", "dayjs": "^1.11.12", "express": "^4.19.2", "express-session": "^1.18.0", "formdata-node": "^6.0.3", "graphql": "^16.9.0", "hashids": "^2.3.0", "humps": "^2.0.1", "ioredis": "^5.4.1", "knex": "^3.1.0", "lodash-es": "^4.17.21", "mathjs": "^13.1.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.0", "qiniu": "^7.14.0", "uuid": "^10.0.0", "xlsx": "^0.18.5", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "eslint": "^9.9.0", "globals": "^15.9.0", "prettier": "^3.3.3"}}