---
description:
globs:
alwaysApply: true
---
## Code Style
- Follow the ESLint configuration in `eslint.config.js`
- Follow the Prettier configuration in `.prettierrc.json`
- Use 2 spaces for indentation
- Use single quotes for strings

## Project Structure
- `/routes`: API route handlers
- `/db`: Database related code and models and dataloader
- `/util`: Utility functions
- `/config`: Configuration files
- `/graphql`: GraphQL schema and resolvers
- `/task`: Background tasks and scheduled jobs
- `/helper`: Helper functions and utilities
