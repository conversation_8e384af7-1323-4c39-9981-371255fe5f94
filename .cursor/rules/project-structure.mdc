---
description:
globs:
alwaysApply: false
---
# Project Structure Guide

The project follows a modular structure with clear separation of concerns:

## Main Components

- Server Entry Point: [server.js](mdc:server.js) - Main application entry point
- API Routes: Located in the [routes/](mdc:routes) directory
- Database: Database models and connections in [db/](mdc:db)
- GraphQL: Schema and resolvers in [graphql/](mdc:graphql)
- Utilities: Helper functions in [util/](mdc:util) and [helper/](mdc:helper)
- Background Tasks: Scheduled jobs in [task/](mdc:task)
- Configuration: Environment and app configs in [config/](mdc:config)

## Configuration Files

- ESLint Config: [eslint.config.js](mdc:eslint.config.js)
- Prettier Config: [.prettierrc.json](mdc:.prettierrc.json)
- PM2 Config: [pm2.config.cjs](mdc:pm2.config.cjs)
- Editor Config: [.editorconfig](mdc:.editorconfig)

## Development Guidelines

1. All API routes should be added to the routes directory
2. Database models should be placed in the db directory
3. Utility functions should be organized in util or helper directories
4. GraphQL schema and resolvers should be maintained in the graphql directory
5. Background tasks and scheduled jobs should be placed in the task directory
