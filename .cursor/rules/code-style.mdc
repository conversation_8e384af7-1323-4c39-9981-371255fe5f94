---
description:
globs:
alwaysApply: false
---
# Code Style and Best Practices

## Code Style

The project follows strict code style guidelines defined in:
- [eslint.config.js](mdc:eslint.config.js)
- [.prettierrc.json](mdc:.prettierrc.json)
- [.editorconfig](mdc:.editorconfig)

### General Rules

1. Use 2 spaces for indentation
2. Use single quotes for strings
3. Use semicolons at the end of statements
4. Maximum line length: 100 characters
5. Use meaningful variable and function names

## Best Practices

### Code Organization

1. Keep files focused and single-purpose
2. Use meaningful directory structure
3. Group related functionality
4. Maintain clear separation of concerns

### Error Handling

1. Use try-catch blocks appropriately
2. Log errors with context
3. Handle promises properly
4. Implement proper error boundaries

### Testing

1. Write unit tests for critical functionality
2. Include integration tests for API endpoints
3. Maintain good test coverage
4. Run tests before committing changes

### Documentation

1. Use JSDoc comments for functions
2. Document complex logic
3. Keep README up to date
4. Include inline comments for non-obvious code

### Git Workflow

1. Use meaningful commit messages
2. Follow conventional commits format
3. Create feature branches for new features
4. Submit pull requests for code review

### Dependencies

1. Keep dependencies up to date
2. Review and remove unused dependencies
3. Use specific versions in package.json
4. Document any special dependency requirements
