scalar ID
scalar Avatar
scalar DateTime
scalar Birth
scalar Image
scalar AppName

directive @auth(roles: [String]) on FIELD_DEFINITION
directive @mpAuth on FIELD_DEFINITION
directive @mpRedirect(auth: Boolean = false) on FIELD_DEFINITION

enum StatusType {
  status
  weight
  meal
  sport
  measurement
  pressure
  glucose
  lipid
  acid
}
enum EntityType {
  status
  comment
  user
}

type Ticket {
  beta: Boolean
  debug: Boolean
  appId: String
  timestamp: String
  nonceStr: String
  signature: String
  jsApiList: [String]
}
type AgentTicket {
  corpid: String
  agentid: String
  timestamp: String
  nonceStr: String
  signature: String
  jsApiList: [String]
}

type Account {
  id: ID
  userId: String
  name: String
  role: String
  qywxUserId: String
  mobile: String
  mpUsers: [User]
}

type User {
  id: ID
  app: AppName
  name: String
  avatar: Avatar
  mobile: String
  gender: Int
  createAt: DateTime
  profiles: [Profile]
  token: String
  classCode: String
}

type Profile {
  id: ID
  self: Boolean
  name: String
  avatar: Image
  gender: Int
  birth: DateTime
  age: String
  weightAt: DateTime
  weight: Float
  bmi: Float
  bf: Float
  height: Float
  heightAt: DateTime
  target: Float
  previousWeight: ProfilePreviousWeight
}
type UserPagedList {
  list: [User]
  total: Int
}

type ProfilePreviousWeight {
  weight: Float
  date: DateTime
  changes: Float
}

type Weight {
  date: DateTime
  previous: Boolean
  weight: Float
  bf: Float
  bmi: Float
}

type Status {
  id: ID
  app: String
  type: StatusType
  content: String
  images: [Image]
  emotion: String
  likeCount: Int
  replyCount: Int
  createAt: DateTime
  user: User
  profile: Profile
  illegal: Boolean
  isPublic: Boolean
  deleted: Boolean
  weight: Weight
  sport: Sport
  meal: Meal
  measurement: Measurement
  pressure: Pressure
  glucose: Glucose
  lipid: Lipid
  acid: Acid
  liked: Boolean
}
type Sport {
  id: ID
  date: DateTime
  previous: Boolean
  sportType: String
  type: String
  duration: Int
  distance: Float
  counts: Int
}
type Meal {
  id: ID
  date: DateTime
  previous: Boolean
  mealType: String
}
type Measurement {
  id: ID
  date: DateTime
  previous: Boolean
  waist: Float
  bust: Float
  hip: Float
}
type Pressure {
  id: ID
  date: DateTime
  previous: Boolean
  systolic: Int
  diastolic: Int
}
type Glucose {
  id: ID
  date: DateTime
  previous: Boolean
  glucose: Float
}
type Lipid {
  id: ID
  date: DateTime
  previous: Boolean
  cholesterol: Float
  triglyceride: Float
  hdl: Float
  ldl: Float
}
type Acid {
  id: ID
  date: DateTime
  previous: Boolean
  acid: Float
}
type StatusPagedList {
  list: [Status]
  total: Int
}

type Comment {
  id: ID
  createAt: DateTime
  status: Status
  user: User
  content: String
  replies: [Comment]
  parent: Comment
  deleted: Boolean
  illegal: Boolean
  mine: Boolean
}

type CommentPagedList {
  list: [Comment]
  total: Int
}
type Like {
  id: ID
  createAt: DateTime
  entityId: ID
  entityType: EntityType
  status: Status
  comment: Comment
  user: User
}
type LikePagedList {
  total: Int
  list: [Like]
}

type MyComment {
  id: ID
  status: Status
  comments: [Comment]
}

type MyCommentPagedList {
  total: Int
  list: [MyComment]
}

type Notification {
  id: ID
  isRead: Boolean
  sender: User
  desc: String
  createAt: DateTime
  category: String
  like: Like
  comment: Comment
  replyComment: Comment
}

type NotificationPagedList {
  total: Int
  list: [Notification]
}

type Plan {
  id: ID
  orderId: ID
  status: Int
  dates: Int
  targetLose: Float
  targetWeight: Float
  startDate: DateTime
  endDate: DateTime
  startWeight: Float
  lastWeightDate: DateTime
  lastWeight: Float
  totalChanges: Float
  createAt: DateTime
  todayIndex: Int
  user: User
  profile: Profile
  isShenPaybackPlan: Int
  shenPaybackEverydayAmount: Int
  shenRewardUsers: [User]
}
type PlanDateWeight {
  date: DateTime
  dateIndex: Int
  weight: Float
  bf: Float
  bmi: Float
  targetWeight: Float
  targetLose: Float
  shenPaybackStatus: Int
  refundInfo: OrderRefund
  commissionInfo: OrderCommission
}
type PlanPagedList {
  list: [Plan]
  total: Int
}
type Order {
  id: ID
  user: User
  status: Int
  shippingStatus: Int
  totalAmount: Int
  discountAmount: Int
  finalAmount: Int
  createAt: DateTime
  updateAt: DateTime
  recipient: OrderRecipient
  prepayId: String
  needShipping: Boolean
  payTransactionId: String
  paySuccessTime: DateTime
  packageName: String
  description: String
  type: Int
  products: [OrderProduct]
  commissions: [OrderCommission]
  refunds: [OrderRefund]
  shippings: [OrderShipping]
}
type OrderRecipient {
  name: String
  phone: String
  province: String
  city: String
  county: String
  town: String
  address: String
}
type OrderProduct {
  id: ID
  productName: String
  productCover: String
  quantity: Int
  originalPrice: Int
  discountPrice: Int
  finalPrice: Int
}
type OrderRefund {
  id: ID
  refundNo: String
  wechatRefundId: String
  channel: String
  status: String
  userReceivedAccount: String
  amount: Int
  createAt: DateTime
  updateAt: DateTime
}
type OrderCommission {
  id: ID
  user: User
  amount: Int
  createAt: DateTime
  updateAt: DateTime
}
type OrderShipping {
  id: ID
  expressCompany: String
  expressCompanyLabel: String
  expressNumber: String
  createAt: DateTime
}
type OrderPagedList {
  list: [Order]
  total: Int
}
type ShenService {
  id: ID
  user: User
  order: Order
  orderId: ID
  referrerUser: User
  referrerUserHasCommission: Boolean
  todayIndex: Int
  start: DateTime
  end: DateTime
  paybackEverydayAmount: Int
  comment: String
  createAt: DateTime
  days: [ShenServiceDay]
}
type ShenServicePagedList {
  list: [ShenService]
  total: Int
}
type ShenServiceDay {
  id: ID
  date: DateTime
  status: Int
  refund: OrderRefund
  referrerCommission: OrderCommission
}
type ChartData {
  time: DateTime
  data: Float
}
type Product {
  id: ID
  name: String
  price: Int
  coverImages: [String]
  descImages: [String]
  status: Int
  isPlan: Boolean
  isShenPaybackPlan: Boolean
  planDates: Int
  cover: String
  desc: String
  shareImage: String
  isShenService: Boolean
  shenServicePaybackEverydayAmount: Int
  apps: [String]
}
type ProductPagedList {
  list: [Product]
  total: Int
}

input ProductInput {
  name: String
  price: Int
  coverImages: [String]
  descImages: [String]
  status: Int
  isPlan: Boolean
  isShenPaybackPlan: Boolean
  planDates: Int
  cover: String
  desc: String
  shareImage: String
  isShenService: Boolean
  shenServicePaybackEverydayAmount: Int
  apps: [String]
}
type Note {
  id: ID
  targetId: ID
  targetType: String
  content: String
  isPublic: Boolean
  createAt: DateTime
  createBy: User
}
type NotePagedList {
  list: [Note]
  total: Int
}
enum RecommendationEntityType {
  product
}
input RecommendationInput {
  app: String!
  category: String!
  orderNum: Int
  entityType: RecommendationEntityType!
  entityId: ID
  cover: String
  page: String
}
type Recommendation {
  app: String
  category: String
  orderNum: Int
  entityType: RecommendationEntityType
  entityId: ID
  cover: String
  page: String
  product: Product
}
type Course {
  id: ID
  name: String
  cover: String
  shareImage: String
  price: Int
  description: String
  totalSections: Int
  totalDuration: Int
  sections: [CourseSection]
  createAt: DateTime
  updateAt: DateTime
  status: Int
}
type CourseSection {
  id: ID
  title: String
  cover: String
  videoUrl: String
  videoSrc: String
  duration: Int
  sectionIndex: Int
  isFreeTrial: Boolean
  course: Course
}
type UserCourse {
  id: ID
  user: User
  course: Course
  order: Order
  purchasedAt: DateTime
  source: String
}
type UserCoursePagedList {
  list: [UserCourse]
  total: Int
}
input CourseInput {
  name: String
  cover: String
  price: Int
  description: String
  totalSections: Int
  totalDuration: Int
  status: Int
  shareImage: String
}
input CourseSectionInput {
  title: String
  cover: String
  videoUrl: String
  duration: Int
  sectionIndex: Int
  isFreeTrial: Boolean
}
type Query {
  qywxTicket: Ticket
  qywxAgentTicket: AgentTicket
  account: Account
  user(id: ID): User @auth
  users(page: Int = 1, size: Int = 20, app: String, name: String, mobile: String, classCode: String, onlyStudent: Boolean = false): UserPagedList @auth
  plans(page: Int = 1, size: Int = 20, app: String, userId: ID, status: Int, userName: String, userMobile: String): PlanPagedList @auth
  planWeights(id: ID): [PlanDateWeight] @auth
  dayWeights(userId: ID!, profileId: ID, start: String, end: String, count: Int = 90): [ChartData] @auth
  statusData(userId: ID!, profileId: ID, statusType: StatusType, dataType: String, start: String, end: String, count: Int = 90): [ChartData] @auth
  order(id: ID!): Order @auth
  orders(page: Int = 1, size: Int = 20, app: String, userId: ID, status: Int, shippingStatus: Int, userName: String, userMobile: String): OrderPagedList @auth
  status(id: ID): Status @auth
  statusList(page: Int = 1, size: Int = 20, app: String, type: String, userId: ID, profileId: ID, public: Boolean, userName: String, userMobile: String): StatusPagedList @auth
  comments(statusId: ID, page: Int, size: Int = 20): CommentPagedList @auth
  """
  打卡的留言
  """
  myLikes(page: Int = 1, size: Int = 20): LikePagedList @auth
  myComments(page: Int = 1, size: Int = 20): MyCommentPagedList @auth
  myNotifications(category: String, page: Int = 1, size: Int = 20): NotificationPagedList @auth
  myUnreadNotificationsCount: Int @auth
  shenServiceList(page: Int = 1, size: Int = 20, userName: String, userMobile: String, userClassCode: String): ShenServicePagedList @auth(roles: ["admin"])
  shenServiceDays(shenServiceId: ID): [ShenServiceDay] @auth(roles: ["admin"])
  products: [Product] @auth(roles: ["admin"])
  recommendations: [Recommendation] @auth(roles: ["admin"])
  qiniuToken(prefix: String!, type: String = "image"): String @auth(roles: ["admin"])
  notes(page: Int = 1, size: Int = 20, targetId: ID!): NotePagedList @auth
  courses: [Course] @auth
  userCourses(page: Int = 1, size: Int = 20, courseId: ID, courseName: String, userId: ID, userName: String, source: String): UserCoursePagedList @auth
}

type Mutation {
  qywxLogin(code: String!): Account
  """
  点赞内容
  """
  like(entityId: ID!, entityType: EntityType!): Boolean @mpRedirect(auth: true)
  """
  取消点赞
  """
  unLike(entityId: ID!, entityType: EntityType!): Boolean @mpRedirect(auth: true)
  """
  发表浏览
  """
  comment(statusId: ID!, parentId: ID, content: String!): Boolean @mpRedirect(auth: true)
  """
  删除评论
  """
  deleteComment(id: ID): Boolean @mpRedirect(auth: true)
  """
  通知已读
  """
  readNotification: Boolean @mpRedirect(auth: true)
  """
  修改计划开始体重
  """
  changePlanStartWeight(id: ID!, weight: Float!): Boolean @auth(roles: ["admin"])
  createPlan(userId: ID!, dates: Int!): Boolean @auth(roles: ["admin"])
  refundOrder(id: ID!, amount: Int): Boolean @auth(roles: ["admin"])
  updateUserClassCode(id: ID!, classCode: String): Boolean @auth(roles: ["admin"])
  addNote(targetId: ID!, targetType: String, note: String, isPublic: Boolean = false): Boolean @auth
  deleteNote(id: ID!): Boolean @auth
  createProduct(product: ProductInput!): Boolean @auth(roles: ["admin"])
  updateProduct(id: ID!, product: ProductInput!): Boolean @auth(roles: ["admin"])
  createRecommendation(input: RecommendationInput): Boolean @auth(roles: ["admin"])
  updateRecommendation(id: ID!, input: RecommendationInput): Boolean @auth(roles: ["admin"])
  deleteRecommendation(id: ID!): Boolean
  shipOrder(orderId: ID!, expressNumber: String, expressCompany: String): Boolean @auth(roles: ["admin"])
  addCourse(course: CourseInput!): Boolean @auth(roles: ["admin"])
  updateCourse(id: ID!, course: CourseInput!): Boolean @auth(roles: ["admin"])
  deleteCourse(id: ID!): Boolean @auth(roles: ["admin"])
  addCourseSection(courseId: ID!, section: CourseSectionInput!): Boolean @auth(roles: ["admin"])
  updateCourseSection(id: ID!, section: CourseSectionInput!): Boolean @auth(roles: ["admin"])
  deleteCourseSection(id: ID!): Boolean @auth(roles: ["admin"])
}
