import { GraphQLError } from 'graphql'
import dayjs from 'dayjs'
import objectSupport from 'dayjs/plugin/objectSupport.js'
import _ from 'lodash-es'
import { round } from 'mathjs'
import config from 'config'
import { getUploadToken, getVideoUploadToken, getAccessUrl } from '../../helper/qiniu.js'

import kn from '../../db/kn.js'
import knOld from '../../db/kn-old.js'
import httpErrorCodes from '../../util/http-error-code.js'
import { getTicketConfig, getAgentTicketConfig } from '../../helper/qywx/ticket-config.js'
import { getQywxUserBaseInfo } from '../../helper/qywx/get-user.js'
import { hashID } from '../../util/hashids.js'
import targetWeight from '../../helper/target-weight.js'
import { requestRefund } from '../../helper/wechat-pay.js'
const { refundNotifyUrl } = config.get('wechat-pay')
import { payOrderCommission } from '../../helper/order.js'

dayjs.extend(objectSupport)

const getOffset = function (page, size) {
  if (!page || page < 1) page = 1
  if (!size || size < 1) size = 20
  return (page - 1) * size
}

export default {
  User: {
    name: (parent) => (!parent.name ? '用户' + hashID.encode(parent.id) : parent.name),
    profiles: (parent, args, { loaders }) => loaders.userProfilesLoader.load(parent.id)
  },
  Plan: {
    user: (parent, args, { loaders }) => loaders.userLoader.load(parent.userId),
    // profile: (parent, args, { loaders }) => loaders.profileLoader.load(parent.profileId),
    todayIndex: ({ status, startDate }) => (status !== 1 ? -1 : dayjs({ hour: 0 }).diff(dayjs(startDate), 'day') + 1),
    shenRewardUsers: async ({ userId, status, isShenPaybackPlan }, arg, { loaders }) => {
      if (status !== 1 || !isShenPaybackPlan) return []
      const user = await loaders.userLoader.load(userId)
      const userIds = user.referrerUserId ? [user.referrerUserId, 10684, 10659] : [10684, 10659]
      return loaders.userLoader.loadMany(userIds)
    },
    shenPaybackEverydayAmount: async ({ id, orderId, status, isShenPaybackPlan, dates }) => {
      if (!isShenPaybackPlan) return 0
      const order = !orderId ? null : await kn('order').where({ id: orderId }).first()
      return order ? Math.floor(order.finalAmount / dates) : 0
    }
  },
  PlanDateWeight: {
    refundInfo: async ({ refundId }, arg, { loaders }) => (refundId ? loaders.orderRefundLoader.load(refundId) : null),
    commissionInfo: async ({ commissionId }, arg, { loaders }) => (commissionId ? loaders.orderCommissionLoader.load(commissionId) : null)
  },
  Status: {
    user: (parent, args, { loaders }) => loaders.userLoader.load(parent.userId),
    profile: (parent, args, { loaders }) => loaders.profileLoader.load(parent.profileId),
    content: (parent) => (parent.illegal ? '内容涉嫌违规' : parent.deleted ? '已删除' : parent.content),
    images: (parent, args, { loaders }) => (parent.illegal || parent.deleted ? [] : loaders.statusImagesLoader.load(parent.id)),
    weight: async ({ id, deleted, illegal, type }, arg, { loaders, account }) => (deleted || illegal || type !== 'weight' ? null : loaders.statusWeightLoader.load(id)),
    sport: async ({ id, deleted, illegal, type }, arg, { loaders }) => (deleted || illegal || type !== 'sport' ? null : loaders.statusSportLoader.load(id)),
    meal: async ({ id, deleted, illegal, type }, arg, { loaders }) => (deleted || illegal || type !== 'meal' ? null : loaders.statusMealLoader.load(id)),
    measurement: async ({ id, deleted, illegal, type }, arg, { loaders }) => (deleted || illegal || type !== 'measurement' ? null : loaders.statusMeasurementLoader.load(id)),
    pressure: async ({ id, deleted, illegal, type }, arg, { loaders }) => (deleted || illegal || type !== 'pressure' ? null : loaders.statusPressureLoader.load(id)),
    glucose: async ({ id, deleted, illegal, type }, arg, { loaders }) => (deleted || illegal || type !== 'glucose' ? null : loaders.statusGlucoseLoader.load(id)),
    lipid: async ({ id, deleted, illegal, type }, arg, { loaders }) => (deleted || illegal || type !== 'lipid' ? null : loaders.statusLipidLoader.load(id)),
    acid: async ({ id, deleted, illegal, type }, arg, { loaders }) => (deleted || illegal || type !== 'acid' ? null : loaders.statusAcidLoader.load(id)),
    liked: async (parent, arg, { loaders }) => (parent.deleted ? false : loaders.statusLikeLoader.load(parent.id))
  },
  Comment: {
    user: async (parent, arg, { loaders }) => loaders.userLoader.load(parent.userId),
    status: async (parent, arg, { loaders }) => loaders.statusLoader.load(parent.statusId),
    parent: async (parent, arg, { loaders }) => (parent.parentId ? loaders.commentLoader.load(parent.parentId) : null),
    mine: async (parent, arg, { account, loaders }) => account.mpUsers.some((i) => i.id === parent.userId)
  },
  Like: {
    status: async ({ entityType, entityId }, arg, { loaders }) => (entityType === 'status' ? loaders.statusLoader.load(entityId) : null),
    comment: async ({ entityType, entityId }, arg, { loaders }) => (entityType === 'comment' ? loaders.commentLoader.load(entityId) : null)
  },
  Notification: {
    sender: async (parent, arg, { loaders }) => (parent.senderId ? loaders.userLoader.load(parent.senderId) : null),
    like: async (parent, arg, { loaders }) => (parent.category === 'like' ? loaders.likeLoader.load(parent.likeId) : null),
    comment: async (parent, arg, { loaders }) => (parent.category === 'comment' ? loaders.commentLoader.load(parent.commentId) : null),
    replyComment: async (parent, arg, { loaders }) => (parent.category === 'comment' && parent.replyCommentId ? loaders.commentLoader.load(parent.replyCommentId) : null)
  },
  Order: {
    user: (parent, args, { loaders }) => loaders.userLoader.load(parent.userId),
    products: async ({ id }, arg, { loaders }) => loaders.orderProductsLoader.load(id),
    commissions: ({ id }, arg, { loaders }) => loaders.orderCommissionsLoader.load(id),
    refunds: ({ id }, arg, { loaders }) => loaders.orderRefundsLoader.load(id),
    shippings: ({ id }, arg, { loaders }) => loaders.orderShippingsLoader.load(id)
  },
  OrderCommission: {
    user: async ({ userId }, arg, { loaders }) => loaders.userLoader.load(userId)
  },
  OrderShipping: {
    expressCompanyLabel: ({ expressCompany }) => (expressCompany === 'JDL' ? '京东快递' : expressCompany)
  },
  ShenService: {
    todayIndex: ({ start, end }) => {
      const today = dayjs({ hour: 0 })
      console.log(start, end.valueOf(), today.valueOf())
      if (end.valueOf() < today.valueOf()) return -1
      return today.diff(dayjs(start), 'day') + 1
    },
    user: (parent, args, { loaders }) => loaders.userLoader.load(parent.userId),
    referrerUser: (parent, args, { loaders }) => (parent.referrerUserId ? loaders.userLoader.load(parent.referrerUserId) : null)
  },
  ShenServiceDay: {
    referrerCommission: ({ referrerCommissionId }, arg, { loaders }) => (referrerCommissionId ? loaders.orderCommissionLoader.load(referrerCommissionId) : null)
  },
  Course: {
    sections: async ({ id }, arg, { loaders }) => loaders.courseSectionsLoader.load(id)
  },
  CourseSection: {
    videoSrc: ({ videoUrl }) => {
      return getAccessUrl(videoUrl + '-course.m3u8', 60 * 60)
    }
  },
  UserCourse: {
    user: (parent, args, { loaders }) => loaders.userLoader.load(parent.userId),
    course: (parent, args, { loaders }) => loaders.courseLoader.load(parent.courseId),
    order: async ({ orderId }, arg, { loaders }) => orderId ? loaders.orderLoader.load(orderId) : null
  },
  Query: {
    qywxTicket: async (_, args, { req }) => getTicketConfig(req.get('referer')),
    qywxAgentTicket: async (_, args, { req }) => getAgentTicketConfig(req.get('referer')),
    account (parent, args, { account }) {
      return account
    },
    user: async (parent, { id }, { account }) => {
      const user = await kn('user').where({ id }).first()
      if (!user) throw new GraphQLError('用户不存在', { extensions: { code: httpErrorCodes.NOT_FOUND } })
      if (account.role === 'seller') {
        if (!account.mpUsers.find((i) => i.id === user.referrerUserId)) throw new GraphQLError('没有权限查看', { extensions: { code: httpErrorCodes.FORBIDDEN } })
      }
      const profiles = await kn('user_profile').where({ userId: user.id })
      return { ...user, profiles }
    },
    users: async (parent, { page, size, app, mobile, name, classCode, onlyStudent }, { account }) => {
      page = page || 1
      size = size || 20
      const qb = kn('user').whereNotNull('mobile')
      if (app) qb.where({ app })
      if (mobile) qb.where('mobile', 'like', `%${mobile}%`)
      if (name) qb.where('name', 'like', `%${name}%`)
      if (classCode) qb.where('classCode', 'like', `%${classCode}%`)
      if (account.role === 'seller') {
        qb.whereIn(
          'referrerUserId',
          account.mpUsers.map((i) => i.id)
        )
      }
      if (onlyStudent) qb.where('classCode', '<>', '').where('app', 'shen')
      const list = await qb.clone().select().offset(getOffset(page, size)).orderBy('id', 'desc').limit(size)
      const [{ total }] = await qb.clone().count({ total: '*' })
      return { list, total }
    },
    plans: async (parent, { page, size, app, userId, status, userName, userMobile }, { account }) => {
      page = page || 1
      size = size || 20
      const qb = kn('plan')
      if (app) qb.where({ app })
      if (userId) qb.where({ userId })
      if (status !== null && status >= 0) qb.where({ status })
      if (account.role === 'seller') {
        qb.whereIn('userId', function () {
          this.select('id')
            .from('user')
            .whereIn(
              'referrerUserId',
              account.mpUsers.map((i) => i.id)
            )
        })
      }
      if (userName) {
        qb.whereIn(
          'userId',
          kn('user')
            .where('name', 'like', '%' + userName + '%')
            .select('id')
        )
      }
      if (userMobile) {
        qb.whereIn(
          'userId',
          kn('user')
            .where('mobile', 'like', '%' + userMobile + '%')
            .select('id')
        )
      }
      const list = await qb.clone().select().offset(getOffset(page, size)).orderBy('id', 'desc').limit(size)
      const [{ total }] = await qb.clone().count({ total: '*' })
      return { list, total }
    },
    planWeights: async (parent, { id }) => {
      const plan = await kn('plan').where({ id }).first()
      if (!plan || plan.status === 0) return []
      const { startDate, endDate } = plan
      const weights = await kn('day_weight')
        .where({ userId: plan.userId, profileId: plan.profileId })
        .where('date', '>=', dayjs(startDate).format('YYYY-MM-DD'))
        .where('date', '<=', dayjs(endDate).format('YYYY-MM-DD'))
        .orderBy('date', 'asc')
      // const targets = await kn('plan_target').where({ planId: plan.id }).orderBy('date', 'asc')
      const targets = await kn('plan_target').where({ planId: plan.id }).orderBy('date', 'asc')
      let j = 0
      const results = targets.map((i, index) => {
        const d = dayjs(i.date).valueOf()
        let weightInfo = null
        for (; j < weights.length; j++) {
          const dv = dayjs(weights[j].date).valueOf()
          if (dv > d) {
            break
          }
          if (dv === d) {
            weightInfo = weights[j]
            j++
            break
          }
        }
        return weightInfo ? { ...i, ..._.pick(weightInfo, 'weight', 'bmi', 'bf', 'previous') } : i
      })
      return results
    },
    dayWeights: async (parent, { userId, profileId, start, end, count }, { account, loaders }) => {
      const user = await loaders.userLoader.load(userId)
      if (!user) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
      const profiles = await loaders.userProfilesLoader.load(userId)
      profileId = profileId || profiles[0].id
      const profile = profiles.find((i) => i.id === profileId)
      if (!profile) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
      const qb = kn('day_weight').where({ userId: user.id, profileId: profile.id }).orderBy('date', 'desc')
      if (start) qb.where('date', '>=', start)
      if (end) qb.where('date', '>=', end)
      if (count > 0) qb.limit(count)
      const list = await qb.select('date', 'weight', 'bf', 'bmi')
      return list.map((i) => ({ time: i.date, data: i.weight })).reverse()
    },
    statusData: async (parent, { userId, profileId, start, end, count }, { account, loaders }) => {
      const user = await loaders.userLoader.load(userId)
      if (!user) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
      const profiles = await loaders.userProfilesLoader.load(userId)
      profileId = profileId || profiles[0].id
      const profile = profiles.find((i) => i.id === profileId)
      if (!profile) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
    },
    order: async (parent, { id }, { account }) => {
      const order = await kn('order').where({ id }).first()
      if (!order) throw new GraphQLError('没有找到', { extensions: { code: httpErrorCodes.NOT_FOUND } })
      if (account.role === 'seller') {
        const ck = await kn('user')
          .whereIn(
            'referrerUserId',
            account.mpUsers.map((i) => i.id)
          )
          .where({ userId: order.userId })
          .first()
        if (!ck) throw new GraphQLError('没有权限', { extensions: { code: httpErrorCodes.FORBIDDEN } })
      }
      return order
    },
    orders: async (parent, { page, size, app, userId, status, shippingStatus, userName, userMobile }, { account }) => {
      page = page || 1
      size = size || 20
      const qb = kn('order')
      if (userId) qb.where({ userId })
      if (app) qb.where({ app })
      if (status) {
        qb.where({ status })
      } else {
        qb.where('status', '>', -1)
      }
      if (userName) {
        qb.whereIn(
          'userId',
          kn('user')
            .where('name', 'like', '%' + userName + '%')
            .select('id')
        )
      }
      if (userMobile) {
        qb.whereIn(
          'userId',
          kn('user')
            .where('mobile', 'like', '%' + userMobile + '%')
            .select('id')
        )
      }
      if (shippingStatus) qb.where({ shippingStatus })
      if (account.role === 'seller') {
        qb.whereIn('userId', function () {
          this.select('id')
            .from('user')
            .whereIn(
              'referrerUserId',
              account.mpUsers.map((i) => i.id)
            )
        })
      }
      const list = await qb.clone().select().orderBy('id', 'desc').offset(getOffset(page, size)).limit(size)
      const [{ total }] = await qb.clone().count({ total: '*' })
      return { list, total }
    },
    statusList: async (parent, { page, size, app, type, userId, userName, userMobile }, { account }) => {
      page = page || 1
      size = size || 20
      const qb = kn('status').where({ deleted: false })
      if (userId) qb.where({ userId })
      if (app) qb.where({ app })
      if (type) qb.where({ type })
      if (account.role === 'seller') {
        qb.whereIn('userId', function () {
          this.select('id')
            .from('user')
            .whereIn(
              'referrerUserId',
              account.mpUsers.map((i) => i.id)
            )
        })
      }
      if (userName) {
        qb.whereIn(
          'userId',
          kn('user')
            .where('name', 'like', '%' + userName + '%')
            .select('id')
        )
      }
      if (userMobile) {
        qb.whereIn(
          'userId',
          kn('user')
            .where('mobile', 'like', '%' + userMobile + '%')
            .select('id')
        )
      }
      const list = await qb.clone().select().offset(getOffset(page, size)).orderBy('id', 'desc').limit(size)
      const [{ total }] = await qb.clone().count({ total: '*' })
      return { list, total }
    },
    comments: async (parent, { statusId, page, size }, { loaders }) => {
      page = page || 1
      size = size || 20
      // 没有 parentId 的评论，即为一级评论
      const qb = kn('comment').where((qb) => {
        if (statusId) qb.where({ statusId, parentId: 0 })
      })
      const { total } = await qb.clone().count({ total: '*' }).first()
      const temps1 = await qb.clone().orderBy('id', 'desc').offset(getOffset(page, size)).limit(size).select(['id', 'parentId'])
      const ids1 = temps1.map((i) => i.id)
      const list = await loaders.commentLoader.loadMany(ids1)
      // 二级评论
      const temps2 =
        ids1.length === 0
          ? []
          : await kn('comment_parent')
            .whereIn(
              'parentId',
              list.map((i) => i.id)
            )
            .select()
      const ids2 = temps2.map((i) => i.commentId)
      const relations = _.reduce(temps2, (memo, i) => ({ ...memo, [i.commentId]: i.parentId }), {})
      let replies = await loaders.commentLoader.loadMany(ids2)
      replies = replies.map((i) => ({ ...i, keyId: relations[i.id] }))
      const repliesGrouped = _.groupBy(replies, 'keyId')
      return { total, list: list.map((i) => ({ ...i, replies: repliesGrouped[i.id] || [] })) }
    },
    myLikes: async (parent, { page, size }, { account }) => {
      const qb = kn('like')
        .whereIn(
          'userId',
          account.mpUsers.map((i) => i.id)
        )
        .where({ deleted: false })
      const { total } = await qb.clone().count({ total: '*' }).first()
      const list = await qb
        .orderBy('id', 'desc')
        .offset(getOffset(page, size))
        .limit((size = 20))
        .select()
      return { total, list }
    },
    myComments: async (parent, { page, size }, { account, loaders }) => {
      // 根据 status id 来分页，一个 status 下的我的留言都拉出来
      const offset = getOffset(page, size)
      const qb = kn('comment')
        .whereIn(
          'userId',
          account.mpUsers.map((i) => i.id)
        )
        .groupBy('statusId')
      const { total } = await qb.clone().count({ total: '*' }).first()
      const temps = await qb.clone().offset(offset).limit(size).orderBy(kn.raw('max(id)'), 'desc').select('statusId', kn.raw('GROUP_CONCAT(id) as ids'))
      if (temps.length === 0) return { total, list: [] }
      const statusIds = temps.map((i) => i.statusId)
      // 这些 status 下，我的所有留言
      const myCommentIds = temps.reduce((memo, i) => memo.concat(i.ids.split(',').map((i) => parseInt(i))), [])
      // 所有的上级父类
      const parents = await kn('comment_parent').whereIn('commentId', myCommentIds).select()
      const commentId2TopId = parents.reduce(
        (memo, item) => ({
          ...memo,
          [item.commentId]: memo[item.commentId] || item.parentId
        }),
        {}
      )
      const status = await loaders.statusLoader.loadMany(statusIds)
      const allCommentId = _.uniq(parents.map((i) => i.parentId).concat(myCommentIds))
      let comments = await loaders.commentLoader.loadMany(allCommentId)
      comments = _.orderBy(comments, 'id', 'desc')
      const commentsGroupedByStatus = {}
      const commentsGroupedByParent = {}
      comments.forEach((comment) => {
        if (comment.parentId) {
          const pid = commentId2TopId[comment.id]
          if (!commentsGroupedByParent[pid]) commentsGroupedByParent[pid] = []
          commentsGroupedByParent[pid].unshift(comment)
        } else {
          if (!commentsGroupedByStatus[comment.statusId]) commentsGroupedByStatus[comment.statusId] = []
          commentsGroupedByStatus[comment.statusId].push(comment)
        }
      })
      const list = status.map((i) => ({
        id: i.id,
        status: i,
        comments: commentsGroupedByStatus[i.id].map((c) => ({ ...c, replies: commentsGroupedByParent[c.id] || [] }))
      }))
      return { total, list }
    },
    myNotifications: async (parent, { category, page, size }, { account }) => {
      const offset = getOffset(page, size)
      const qb = kn('notification').whereIn(
        'userId',
        account.mpUsers.map((i) => i.id)
      )
      if (category) qb.where({ category })
      const { total } = await qb.clone().count({ total: '*' }).first()
      const list = await qb.clone().orderBy('id', 'desc').offset(offset).limit(size).select()
      await qb.clone().where({ isRead: false }).update({ isRead: true })
      return { list, total }
    },
    myUnreadNotificationsCount: async (parent, args, { account }) => {
      const count = await kn('notification')
        .where({ isRead: false })
        .whereIn(
          'userId',
          account.mpUsers.map((i) => i.id)
        )
        .count({ count: 'id' })
        .first()
      return count.count
    },
    shenServiceList: async (parent, { userName, userMobile, userClassCode, page, size }, { account, loaders }) => {
      const offset = getOffset(page, size)
      const qb = kn('shen_service')
      if (userName) {
        qb.whereIn(
          'userId',
          kn('user')
            .where('name', 'like', '%' + userName + '%')
            .select('id')
        )
      }
      if (userMobile) {
        qb.whereIn(
          'userId',
          kn('user')
            .where('mobile', 'like', '%' + userMobile + '%')
            .select('id')
        )
      }
      if (userClassCode) {
        qb.whereIn(
          'userId',
          kn('user')
            .where('classCode', 'like', '%' + userClassCode + '%')
            .select('id')
        )
      }
      const { total } = await qb.clone().count({ total: '*' }).first()
      const list = await qb.clone().orderBy('id', 'desc').offset(offset).limit(size).select()
      return { list, total }
    },
    shenServiceDays: async (parent, { shenServiceId }) => {
      const item = await kn('shen_service').where({ id: shenServiceId }).first()
      if (!item) throw new GraphQLError('服务不存在', { extensions: { code: httpErrorCodes.NOT_FOUND } })
      const days = await kn('shen_service_day').where({ shenServiceId }).where('date', '<=', dayjs().format('YYYY-MM-DD')).orderBy('date', 'desc')
      return days
    },
    products: async (parent, args, { account }) => {
      const list = await kn('product').select()
      const appRows = await kn('product_app').select()
      const apps = appRows.reduce((memo, i) => {
        if (!memo[i.productId]) memo[i.productId] = []
        memo[i.productId].push(i.app)
        return memo
      }, {})
      return list.map((i) => ({ ...i, apps: apps[i.id] }))
    },
    recommendations: async (parent, args, { account }) => {
      return kn('recommendation').orderBy('orderNum').select()
    },
    qiniuToken: async (parent, { prefix, type }, { account }) => {
      // if (prefix !== 'avatar' && prefix !== 'status') return new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      return type === 'video' ? getVideoUploadToken(prefix) : getUploadToken(prefix)
    },
    courses: async (parent, args, { account }) => {
      return kn('course').select()
    },
    userCourses: async (parent, { page, size, courseId, courseName, userId, userName, source }, { account }) => {
      const offset = getOffset(page, size)
      const qb = kn('user_course')
      if (courseId) qb.where({ courseId })
      if (courseName) {
        qb.whereIn(
          'courseId',
          kn('course')
            .where('name', 'like', '%' + courseName + '%')
            .select('id')
        )
      }
      if (userId) qb.where({ userId })
      if (userName) {
        qb.whereIn(
          'userId',
          kn('user')
            .where('name', 'like', '%' + userName + '%')
            .select('id')
        )
      }
      if (source) qb.where({ source })
      const { total } = await qb.clone().count({ total: '*' }).first()
      const list = await qb.clone().orderBy('id', 'desc').offset(offset).limit(size).select()
      return { list, total }
    }
  },
  Mutation: {
    qywxLogin: async (_, { code }, { account, req }) => {
      if (account) return account
      const { userid } = await getQywxUserBaseInfo(code)
      if (!userid) throw new GraphQLError('没有权限登录', { extensions: { code: httpErrorCodes.CUSTOM } })
      const user = await knOld('manage_user').where({ userId: userid }).first()
      if (!user) throw new GraphQLError('没有权限登录', { extensions: { code: httpErrorCodes.CUSTOM } })
      const mpUsers = await kn('user').where({ mobile: user.mobile })
      if (mpUsers.length === 0) {
        throw new GraphQLError('用户还没有注册小程序', { extensions: { code: httpErrorCodes.CUSTOM } })
      }
      req.session.user = userid
      return { ...user, mpUsers }
    },
    changePlanStartWeight: async (parent, { id, weight }, { account }) => {
      const plan = await kn('plan').where({ id }).first()
      if (!plan) throw new GraphQLError('计划不存在或权限不足', { extensions: { code: httpErrorCodes.NOT_FOUND } })
      if (plan.status !== 1) throw new GraphQLError('计划状态不正确', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
      const profile = await kn('user_profile').where({ id: plan.profileId }).first()
      const targets = targetWeight(plan.dates, profile.height, weight)
      await kn.transaction(async function (trx) {
        await trx('plan')
          .where({ id })
          .update({
            startWeight: weight,
            targetWeight: targets[targets.length - 1].targetWeight,
            targetLose: round(weight - targets[targets.length - 1].targetWeight, 2),
            totalChanges: kn.raw('CASE WHEN lastWeight IS NOT NULL THEN lastWeight - ? ELSE NULL END', [weight])
          })
        const values = targets.map((i, index) => ({
          dateIndex: index + 1,
          targetWeight: i.targetWeight,
          targetLose: i.targetLose,
          date: dayjs(plan.startDate).add(index, 'day').format('YYYY-MM-DD')
        }))
        for (const v of values) {
          const { dateIndex, ...others } = v
          await trx('plan_target').where({ planId: id, dateIndex }).update(others)
        }
      })
      return true
    },
    createPlan: async (parent, { userId, dates }, { account }) => {
      const user = await kn('user').where({ id: userId }).first()
      if (!user || (dates !== 28 && dates !== 84)) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
      const info = { userId: user.id, orderId: 0, status: 0, app: user.app, dates }
      await kn('plan').insert(info)
      return true
    },
    refundOrder: async (parent, { id, amount }) => {
      const order = await kn('order').where({ id }).first()
      if (!order || order.status < 1) throw new GraphQLError('参数错误', { code: httpErrorCodes.NOT_FOUND })
      const existRefund = await kn('order_refund').where({ orderId: id }).sum({ amount: 'amount' }).count({ count: 'id' }).first()
      console.log(amount, existRefund)
      if (order.finalAmount - (existRefund.amount ?? 0) < amount) throw new GraphQLError('金额大于实付金额减已退款金额', { code: httpErrorCodes.CUSTOM })
      await kn.transaction(async function (trx) {
        const refundNo = `${order.id}_${dayjs().format('YYYYMMDD')}_${existRefund.count + 1}`
        const [id] = await trx('order_refund').insert({
          orderId: order.id,
          refundNo,
          amount,
          status: 'PROCESSING',
          createAt: kn.raw('CURRENT_TIMESTAMP')
        })
        const info = {
          transaction_id: order.payTransactionId,
          out_refund_no: refundNo,
          reason: '退款',
          amount: {
            refund: amount,
            total: order.finalAmount,
            currency: 'CNY'
          },
          notify_url: refundNotifyUrl
        }
        const result = await requestRefund(order.app, info)
        const update = {
          status: result.status,
          wechatRefundId: result.refund_id,
          channel: result.channel,
          userReceivedAccount: result.user_received_account
        }
        if (result.status === 'SUCCESS') {
          update.updateAt = dayjs(result.success_time).format('YYYY-MM-DD HH:mm:ss')
        }
        await trx('order_refund').where({ id }).update(update)
      })
      return true
    },
    payOrderCommission: async (parent, { id }) => {
      await payOrderCommission(id)
      return true
    },
    updateUserClassCode: async (parent, { id, classCode }) => {
      const user = await kn('user').where({ id }).first()
      if (!user) throw new GraphQLError('用户不存在', { extensions: { code: httpErrorCodes.NOT_FOUND } })
      if (user.app !== 'shen' || classCode === null) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
      await kn('user').where({ id }).update({ classCode })
      return true
    },
    addNote: async (parent, { targetId, targetType, content, isPublic }, { account }) => {
      const types = ['user', 'order', 'plan', 'shen_service']
      if (!types.includes(targetType)) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
      const target = await kn(targetType).where({ id: targetId }).first()
      if (!target) throw new GraphQLError('对象不存在', { extensions: { code: httpErrorCodes.NOT_FOUND } })
      await kn('admin_note').insert({
        targetId,
        targetType,
        content,
        createBy: account.id,
        isPublic
      })
      return true
    },
    deleteNote: async (parent, { id }, { account }) => {
      const note = await kn('admin_note').where({ id }).first()
      if (!note) throw new GraphQLError('笔记不存在', { extensions: { code: httpErrorCodes.NOT_FOUND } })
      if (note.createBy !== account.id) throw new GraphQLError('笔记不存在', { extensions: { code: httpErrorCodes.NOT_FOUND } })
      await kn('admin_note').where({ id }).delete()
      return true
    },
    createProduct: async (parent, { product }, { account }) => {
      const keys = [
        'name',
        'price',
        'coverImages',
        'descImages',
        'status',
        'isPlan',
        'isShenService',
        // 'isShenPaybackPlan',
        'cover',
        'shareImage',
        'apps'
      ]
      if (keys.some((i) => product[i] === undefined || product[i] === null) || (product.isPlan && !product.planDates) || (product.isShenService && !product.shenServicePaybackEverydayAmount)) {
        console.log(keys.find((i) => product[i] === undefined || product[i] === null))
        throw new GraphQLError('参数错误', { extensions: { code: 400 } })
      }
      await kn.transaction(async (trx) => {
        const { apps, ...p } = product
        p.supplierId = 1
        p.coverImages = JSON.stringify(p.coverImages)
        p.descImages = JSON.stringify(p.descImages)
        const [id] = await trx('product').insert(p)
        await trx('product_app').insert(apps.map((i) => ({ productId: id, app: i })))
      })
    },
    updateProduct: async (parent, { id, product }, { account }) => {
      if (Object.keys(product).length === 0) throw new GraphQLError('参数错误', { extensions: { code: 400 } })
      if ((product.isPlan && !product.planDates) || (product.isShenService && !product.shenServicePaybackEverydayAmount)) {
        throw new GraphQLError('参数错误', { extensions: { code: 400 } })
      }
      const p = await kn('product').where({ id }).first()
      if (!p) throw new GraphQLError('产品没有找到', { extensions: { code: 404 } })
      const { apps, ...productInfo } = product
      console.log(apps)
      console.log(productInfo)
      await kn.transaction(async (trx) => {
        if (productInfo.coverImages) {
          productInfo.coverImages = JSON.stringify(productInfo.coverImages)
        }
        if (productInfo.descImages) {
          productInfo.descImages = JSON.stringify(productInfo.descImages)
        }
        await trx('product').where({ id }).update(productInfo)
        if (apps.length > 0) {
          await trx('product_app').where({ productId: id }).delete()
          await trx('product_app').insert(apps.map((i) => ({ productId: id, app: i })))
        }
      })
    },
    createRecommendation: async (parent, { input }, { account }) => {
      if (!input.orderNum) input.orderNum = 100
      if (input.entityType === 'product') {
        const p = await kn('product').where({ id: input.entityId }).first()
        if (!p) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
        input.page = '/pages/shop/product/item?id=' + hashID.encode(input.entityId)
        if (!input.cover) input.cover = p.cover
      }
      await kn('recommendation').insert(input)
      return true
    },
    updateRecommendation: async (parent, { id, input }, { account }) => {
      if (input.entityType === 'product') {
        const p = await kn('product').where({ id: input.entityId }).first()
        if (!p) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
        input.page = '/pages/shop/product/item?id=' + hashID.encode(input.entityId)
        if (!input.cover) input.cover = p.cover
      }
      if (!_.isEmpty(input)) {
        await kn('recommendation').where({ id }).update(input)
      }
    },
    deleteRecommendation: async (parent, { id }, { account }) => {
      await kn('recommendation').where({ id }).delete()
    },
    shipOrder: async (parent, { orderId, expressCompany, expressNumber }, { account }) => {
      const order = await kn('order').where({ id: orderId }).first()
      if (!order || !order.needShipping || order.status < 1) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
      const shipping = await kn('order_shipping').where({ orderId, expressCompany, expressNumber }).first()
      if (shipping) throw new GraphQLError('该订单已存在该单号', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
      await kn.transaction(async (trx) => {
        if (order.status === 1) {
          await trx('order').where({ id: orderId }).update({ status: 2 })
          await trx('order_shipping').insert({ orderId, expressCompany, expressNumber })
        }
      })
      return true
    },
    addCourse: async (parent, { course }, { account, loaders }) => {
      await kn('course').insert(course)
      return true
    },
    updateCourse: async (parent, { id, course }, { account, loaders }) => {
      await kn('course').where({ id }).update(course)
      return true
    },
    deleteCourse: async (parent, { id }, { account }) => {
      await kn.transaction(async (trx) => {
        // 先删除课程的所有 sections
        await trx('course_section').where({ courseId: id }).delete()
        // 再删除课程本身
        await trx('course').where({ id }).delete()
      })
      return true
    },
    addCourseSection: async (parent, { courseId, section }, { account, loaders }) => {
      await kn('course_section').insert({ courseId, ...section })
      return true
    },
    updateCourseSection: async (parent, { id, section }, { account, loaders }) => {
      const s = await kn('course_section').where({ id }).first()
      if (!s) throw new GraphQLError('课时不存在', { extensions: { code: httpErrorCodes.NOT_FOUND } })
      await kn('course_section').where({ id }).update(section)
      return true
    },
    deleteCourseSection: async (parent, { id }, { account }) => {
      const s = await kn('course_section').where({ id }).first()
      if (!s) throw new GraphQLError('课时不存在', { extensions: { code: httpErrorCodes.NOT_FOUND } })
      await kn('course_section').where({ id }).delete()
      return true
    }
  }
}
