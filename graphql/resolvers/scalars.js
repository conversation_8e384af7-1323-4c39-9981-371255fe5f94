import { GraphQLScalarType } from 'graphql'
import dayjs from 'dayjs'
import config from 'config'
import { hashID } from '../../util/hashids.js'
const imgHost = config.get('imgHost')

const ID = new GraphQLScalarType({
  name: 'ID',
  serialize: (v) => v ? hashID.encode(v) : '',
  parseValue: (v) => hashID.decode(v)[0] ?? null
})

const DateTime = new GraphQLScalarType({
  name: 'DateTime',
  serialize: (v) => !v ? null : typeof v === 'string' ? dayjs(v).valueOf() : v.valueOf(),
  parseValue: (v) => v ? new Date(v) : null
})

const Avatar = new GraphQLScalarType({
  name: 'Avatar',
  serialize: (v) => {
    if (!v) return ''
    if (v.startsWith('http')) {
      if (v.includes(imgHost) || v.includes('imgs.yiweimatou.com') || v.includes('ywmatou.oss')) return v + '!avatar'
      return v
    }
    return `${imgHost}${v}-avatar.jpg`
  },
  parseValue: (v) => v,
  parseLiteral: (ast) => ast.value
})

const AppName = new GraphQLScalarType({
  name: 'AppName',
  serialize: (v) => v === 'tqq' ? '体轻轻' : v === 'shen' ? '沈教授' : v === 'mayi' ? '蚂蚁瘦瘦' : '未知'
})

export default {
  ID,
  DateTime,
  Avatar,
  AppName
}
