import kn from '../db/kn.js'
import { GraphQLError } from 'graphql'
import httpErrorCodes from '../util/http-error-code.js'
import config from 'config'
import { profitShareOrder, transferToUser } from './wechat-pay.js'

export async function payOrderCommission (orderId) {
  const order = await kn('order').where({ id: orderId }).first()
  if (!order) throw new GraphQLError('订单不存在', { extensions: { code: httpErrorCodes.NOT_FOUND } })
  const waitings = await kn('order_commission').where({ orderId, status: 'WAITING' }).select()
  if (!waitings.length) throw new GraphQLError('没有未支付的佣金', { extensions: { code: httpErrorCodes.BAD_REQUEST } })
  const appId = config.get('mp')[order.app].appID
  const orderUser = await kn('user').where({ id: order.userId }).first()
  for (const item of waitings) {
    const user = await kn('user').where({ id: item.userId }).first()
    const description = item.comment || `现金奖励（订单：${order.orderNo}, 用户:${orderUser.name}）`
    let leftAmount = item.amount
    // 先使用分账 profit share，分账金额为订单金额的 30%
    const profitAmount = Math.min(leftAmount, order.finalAmount * 0.3)
    leftAmount -= profitAmount
    await kn.transaction(async trx => {
      // 判断是否已经处理过分账，todo 判断是否分账成功，如果失败，需要重新分账
      const exists = await trx('order_commission_payment').where({ orderCommissionId: item.id, payMethod: 'PROFIT' }).first()
      if (exists) return
      const [sharingId] = await trx('wechat_profit_sharing').insert({ appId, transactionId: order.payTransactionId, outOrderNo: order.orderNo, state: 'WAITING' })
      const [sharingReceiverId] = await trx('wechat_profit_sharing_receiver').insert({ profitSharingId: sharingId, type: 'PERSONAL_OPENID', account: user.openId, amount: profitAmount, description })
      await trx('order_commission_payment').insert({ orderCommissionId: item.id, amount: profitAmount, payMethod: 'PROFIT', profitSharingId: sharingId, profitSharingReceiverId: sharingReceiverId, status: 'WAITING' })
      const receivers = [{ openId: user.openId, amount: profitAmount, description }]
      const result = await profitShareOrder(appId, order.payTransactionId, order.orderNo, receivers)
      await trx('wechat_profit_sharing').where({ id: sharingId }).update({ state: result.state, orderId: result.orderId })
      for (const r of result.receivers) {
        await trx('wechat_profit_sharing_receiver')
          .where({ profitSharingId: sharingId, account: r.account })
          .update({ result: r.result, failReason: r.failReason, createTime: r.createTime, finishTime: r.finishTime, detailId: r.detailId })
      }
      if (leftAmount === 0) {
        await trx('order_commission').where({ id: item.id }).update({ status: 'PROCESSING' })
      }
    })
    // 处理分账结束，开始转账（如果金额操作订单金额 30%）
    if (leftAmount > 0) {
      
      await kn.transaction(async trx => {
        const [transferId] = await trx('wechat_pay_transfer').insert({ appId, transferSceneId: '1000', batchName: '现金奖励', batchRemark: '现金奖励', totalAmount: leftAmount, totalNum: 1, batchStatus: 'WAITING' })
        const outBatchNo = 'commission' + transferId.toString().padStart(5, '0')
        // await trx('wechat_pay_transfer').where({ id: transferId }).update({ outBatchNo })
        const [transferDetailId] = await trx('wechat_pay_transfer_detail').insert({ transferId, openId: user.openId, transferAmount: leftAmount, transferRemark: description })
        const outDetailNo = 'commission' + transferDetailId.toString().padStart(5, '0')
        await trx('wechat_pay_transfer_detail').where({ id: transferDetailId }).update({ outDetailNo })
        await trx('order_commission_payment').insert({ orderCommissionId: item.id, amount: leftAmount, payMethod: 'TRANSFER', transferId, transferDetailId, status: 'WAITING' })
        const transInfo = {
          appid: appId,
          out_batch_no: outBatchNo,
          batch_name: '现金奖励',
          batch_remark: '现金奖励',
          total_amount: leftAmount,
          total_num: 1,
          transfer_scene_id: '1000',
          notify_url: config.get('wechat-pay').transferNotifyUrl,
          transfer_detail_list: [{
            openid: user.openId,
            out_detail_no: outDetailNo,
            transfer_amount: leftAmount,
            transfer_remark: description
          }]
        }
        const { batch_id: batchId, create_time: createTime, batch_status: batchStatus } = await transferToUser(transInfo)
        await trx('wechat_pay_transfer').where({ id: transferId }).update({ batchId, createTime, batchStatus, outBatchNo })
        await trx('order_commission').where({ id: item.id }).update({ status: 'PROCESSING' })
      })
    }
  }
}
