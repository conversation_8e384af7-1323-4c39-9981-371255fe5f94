import kn from '../db/kn.js'
import { GraphQLError } from 'graphql'
import config from 'config'
import httpErrorCodes from '../util/http-error-code.js'
import { transferToUser } from './wechat-pay.js'
import dayjs from 'dayjs'
const mpConfig = config.get('mp')

export const payCommission = async (planId) => {
  const plan = await kn('plan').where('id', planId).first()
  if (!plan || plan.status === 0) {
    throw new GraphQLError('计划不存在或未启动', { extensions: { code: httpErrorCodes.CUSTOM } })
  }
  if (!plan.isShenPaybackPlan) {
    throw new GraphQLError('非打赏计划', { extensions: { code: httpErrorCodes.CUSTOM } })
  }
  if (plan.commissionStatus === 2) {
    throw new GraphQLError('已支付', { extensions: { code: httpErrorCodes.CUSTOM } })
  }
  const order = await kn('order').where('id', plan.orderId).first()
  if (!order) {
    throw new GraphQLError('没有订单', { extensions: { code: httpErrorCodes.CUSTOM } })
  }
  const planUser = await kn('user').where('id', plan.userId).first()
  const waitingCommissions = await kn('order_commission')
    .join('user', 'user.id', 'order_commission.userId')
    .where({ orderId: plan.orderId, status: 'WAITING' })
    .where('user.id', '!=', 10659) // 去掉沈老师
    .select('order_commission.id', 'user.openId', 'order_commission.amount')
  if (waitingCommissions.length === 0) {
    console.log('没有未支付的打赏', planId)
    await kn('plan').where('id', planId).update({ commissionStatus: 2 })
    return
    // throw new GraphQLError('没有未支付的打赏', { extensions: { code: httpErrorCodes.CUSTOM } })
  }
  const comments = `计划打赏（${planUser.name.length > 10 ? planUser.name.slice(0, 10) + '...' : planUser.name}${dayjs(planUser.startDate).format('MM月DD日')}-${dayjs(planUser.endDate).format('MM月DD日')}）`
  const commission = waitingCommissions.reduce((acc, i) => acc + i.amount, 0)
  await kn.transaction(async (trx) => {
    const appId = mpConfig[plan.app].appID
    const sceneId = '1000'
    const batchName = `计划${plan.id}打赏`
    const batchRemark = `计划${plan.id}打赏`
    const totalAmount = commission
    const totalNum = waitingCommissions.length
    const [transferId] = await trx('wechat_pay_transfer').insert({ appId, sceneId, batchName, batchRemark, totalAmount, totalNum, batchStatus: 'WAITING' })
    for (const i of waitingCommissions) {
      const [detailId] = await trx('wechat_pay_transfer_detail').insert({ transferId, openId: i.openId, amount: i.amount, remark: comments })
      await trx('order_commission').where('id', i.id).update({ transferId, transferDetailId: detailId })
      i.detailId = detailId
    }
    const transInfo = {
      appid: appId,
      out_batch_no: 'commission' + transferId.toString().padStart(5, '0'),
      batch_name: batchName,
      batch_remark: batchRemark,
      total_amount: totalAmount,
      total_num: totalNum,
      transfer_scene_id: '1000',
      notify_url: config.get('wechat-pay').transferNotifyUrl,
      transfer_detail_list: waitingCommissions.map(i => ({
        openid: i.openId,
        out_detail_no: 'commission' + i.detailId.toString().padStart(5, '0'),
        transfer_amount: i.amount,
        transfer_remark: comments
      }))
    }
    console.log(transInfo)
    const { batch_id: batchId, create_time: createTime, batch_status: batchStatus } = await transferToUser(transInfo)
    await trx('wechat_pay_transfer').where('id', transferId).update({ batchId, createTime, batchStatus })
    await trx('order_commission').where({ transferId }).update({ status: batchStatus })
    await trx('plan').where('id', planId).update({ commissionStatus: 2 })
  })
}

export const payPreviousCommissions = async () => {
  // select * from plan where status = 2 and isShenPaybackPlan = 1 and commissionStatus = 0
  const plans = await kn('plan').where({ status: 2, isShenPaybackPlan: 1, commissionStatus: 0 })
  for (const plan of plans) {
    console.log('start plan', plan.id)
    await payCommission(plan.id)
  }
}
