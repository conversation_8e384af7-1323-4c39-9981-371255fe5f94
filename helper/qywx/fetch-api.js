import axios from 'axios'
import humps from 'humps'
import { getAppToken } from './get-token.js'

export async function fetchAppApi (request) {
  const token = await getAppToken()
  if (typeof request === 'string') {
    request = { url: `${request}${request.indexOf('?') >= 0 ? '&' : '?'}access_token=${token}` }
  } else if (!request.url) {
    throw new Error('没有请求的地址')
  } else {
    request.url = `${request.url}${request.url.indexOf('?') >= 0 ? '&' : '?'}access_token=${token}`
  }
  request.timeout = 30000
  console.log('request', request)
  const res = await axios.request(request)
  console.log(res.status, res.data)
  const { errcode, errmsg, ...rest } = res.data
  if (errcode) throw new Error(res.data.errmsg)
  return humps.camelizeKeys(rest)
}
