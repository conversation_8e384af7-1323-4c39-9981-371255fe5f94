import axios from 'axios'
import config from 'config'
import redis from '../../db/redis.js'

const { corpId, ...others } = config.get('qywx')

async function getToken (key) {
  const k = `qywx:access_token:${key}`
  const token = await redis.get(k)
  if (token) return token
  const url = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpId}&corpsecret=${others[key + 'Secret']}`
  const res = await axios.get(url)
  if (res.data.errcode) {
    throw new Error(res.data.errmsg)
  }
  const token2 = res.data.access_token
  await redis.set(k, token2, 'EX', res.data.expires_in - 20)
  return token2
}

export async function getAppToken () {
  return getToken('app')
}
