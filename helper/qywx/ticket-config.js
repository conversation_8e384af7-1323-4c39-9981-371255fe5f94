import config from 'config'
import { fetchAppApi } from './fetch-api.js'
import redis from '../../db/redis.js'
import { hashSha1 } from '../../util/encrypt.js'

const { corpId, agentId } = config.get('qywx')
const createNonceStr = () => Math.random().toString(36).substring(2, 18)
const createTimestamp = () => Math.floor(new Date().getTime() / 1000) + ''

// https://developer.work.weixin.qq.com/document/path/90539#%E8%8E%B7%E5%8F%96%E5%BA%94%E7%94%A8%E7%9A%84jsapi-ticket
async function getTicket () {
  const key = 'qywx_ticket'
  const storedTicket = await redis.get(key)
  if (storedTicket) return storedTicket
  const { ticket, expiresIn } = await fetchAppApi('https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket')
  await redis.set(key, ticket, 'EX', expiresIn - 10)
  return ticket
}

async function getAgentTicket () {
  const key = 'qywx_agent_ticket'
  const storedTicket = await redis.get(key)
  if (storedTicket) return storedTicket
  const { ticket, expiresIn } = await fetchAppApi('https://qyapi.weixin.qq.com/cgi-bin/ticket/get?type=agent_config')
  await redis.set(key, ticket, 'EX', expiresIn - 10)
  return ticket
}

export async function getTicketConfig (refererUrl) {
  const ticket = await getTicket()
  // const url = req.get('referer')
  const nonceStr = createNonceStr()
  const timestamp = createTimestamp()
  const str = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${refererUrl}`
  const signature = hashSha1(str)
  const model = {
    beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
    debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
    appId: corpId, // 必填，企业微信的corpID
    timestamp, // 必填，生成签名的时间戳
    nonceStr, // 必填，生成签名的随机串
    signature, // 必填，签名，见 附录-JS-SDK使用权限签名算法
    jsApiList: [
      'openUserProfile',
      'scanQRCode',
      'selectEnterpriseContact',
      'openEnterpriseChat',
      'chooseInvoice',
      'selectExternalContact',
      'getCurExternalContact',
      'openUserProfile',
      'hideAllNonBaseMenuItem',
      'getContext',
      'launchMiniprogram'
    ] // 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
  }
  return model
}

export async function getAgentTicketConfig (refererUrl) {
  const ticket = await getAgentTicket()
  // const url = req.get('referer')
  const nonceStr = createNonceStr()
  const timestamp = createTimestamp()
  const str = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${refererUrl}`
  const signature = hashSha1(str)
  const model = {
    corpid: corpId, // 必填，企业微信的corpid，必须与当前登录的企业一致
    agentid: agentId, // 必填，企业微信的应用id
    timestamp, // 必填，生成签名的时间戳
    nonceStr, // 必填，生成签名的随机串
    signature, // 必填，签名，见附录1
    jsApiList: [
      'selectExternalContact',
      'openUserProfile',
      'shareToExternalContact',
      'sendChatMessage',
      'getCurExternalContact',
      'getCurExternalChat',
      'applyCodeForCreateChat',
      'getContext',
      'launchMiniprogram'
    ] // 必填
  }
  return model
}
