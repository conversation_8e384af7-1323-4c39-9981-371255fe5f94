import { fetchAppApi } from './fetch-api.js'

export async function getQywxUserBaseInfo (code) {
  return fetchAppApi(`https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?code=${code}`)
}

// https://developer.work.weixin.qq.com/document/path/95833
export async function getQywxUserDetailInfo (userTicket) {
  // const { user_ticket } = await fetchAppApi(`https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?code=${code}`)
  return fetchAppApi({ method: 'post', url: 'https://qyapi.weixin.qq.com/cgi-bin/auth/getuserdetail', data: { user_ticket: userTicket } })
}
