import { FormData } from 'formdata-node'
import { fileFromPath } from 'formdata-node/file-from-path'
import redis from '../../db/redis.js'
import { fetchAppApi } from './fetch-api.js'

export async function uploadImageByBuffer (imageBuffer) {
  const form = new FormData()
  form.append('media', imageBuffer)
  form.append('filename', 'img.jpg')
  const info = await fetchAppApi({ url: 'https://qyapi.weixin.qq.com/cgi-bin/media/upload?type=image', method: 'POST', data: form })
  return info.mediaId
}

export async function getImageMediaIdByPath (path) {
  const key = `qywx:media:${path}`
  const id = await redis.get(key)
  if (id) return id
  const media = await fileFromPath(path)
  const mediaId = await uploadImageByBuffer(media)
  await redis.set(key, mediaId, 'EX', 60 * 60 * 24 * 3 - 20) // 缓存3天-20 秒
  return mediaId
}
