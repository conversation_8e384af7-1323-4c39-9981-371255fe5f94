// import kn from '../db/kn.js'
import config from 'config'
// const { agentId } = config.get('qywx')
import { fetchAppApi } from './fetch-api.js'
import { getImageMediaIdByPath } from './media.js'

export async function sendMessage (data) {
  await fetchAppApi({ url: 'https://qyapi.weixin.qq.com/cgi-bin/message/send', data, method: 'post' })
}

export async function sendWelcomeMsg (data) {
  await fetchAppApi({ url: 'https://qyapi.weixin.qq.com/cgi-bin/externalcontact/send_welcome_msg', method: 'POST', data })
}

export async function sendWelcomeMessage (welcomeCode, qywxUserId, externalUserID) {
  // todo 不同的企业微信用户发送不同的消息
  if (qywxUserId === 'ShenYueLiang' || qywxUserId === 'TaoXiaoLei') {
    const { shareImage, appID } = config.get('mp').shen
    const imgPath1 = `${process.cwd()}/config/imgs/${shareImage}`
    const mediaId1 = await getImageMediaIdByPath(imgPath1)
    const imgPath2 = `${process.cwd()}/config/imgs/p2.jpg`
    const mediaId2 = await getImageMediaIdByPath(imgPath2)
    const data = {
      welcome_code: welcomeCode,
      text: {
        content: `    你好，我是已退休了的浙大沈老师（人体生理学教授，病理生理生学教授，研究方向是心血管），愿意跟大家一起做健康大事业，也希望对你的健康有指导和促进！
    这里你是主动的，我沈老师是被动的，我可能没有及时看到信息，不会及时回复，你可以在这里留言和汇报你的健康情况，我有空时会关注的。
    我有记录个体代谢的小程序“沈教授的代谢训练营”，你可以用 “沈教授的代谢训练营” 小程序来记录自己的代谢数据。
    这个小程序适用：减肥、脂肪肝、胆结石、高血糖、高血脂、高血压和血管斑块逆转的用户，用户可先自学和实践，也可以参加HEALTH积优生活的代谢逆转学习（365天，6765元，每天打卡了回款6.18元的代谢学习小组）。等代谢指标正常后，再一起践行“生日后年度代谢调食月”，或称“沈教授的代谢调食月”`
      },
      attachments: [{
        msgtype: 'miniprogram',
        miniprogram: {
          title: '沈教授的代谢训练营',
          pic_media_id: mediaId1,
          appid: appID,
          page: '/pages/home/<USER>'
        }
      }, {
        msgtype: 'miniprogram',
        miniprogram: {
          title: '浙大沈教授“HEALTH积优生活”教育（一年）',
          pic_media_id: mediaId2,
          appid: appID,
          page: '/pages/shop/product/item?id=r9djzpmbqn'
        }
      }]
    }
    console.log(data)
    await sendWelcomeMsg(data)
  }
}
