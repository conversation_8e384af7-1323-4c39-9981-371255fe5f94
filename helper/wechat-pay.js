import fs from 'node:fs/promises'
import crypto from 'node:crypto'
import config from 'config'
import axios from 'axios'
import { GraphQLError } from 'graphql'
import httpErrorCodes from '../util/http-error-code.js'
import humps from 'humps'

const { mchId, mchKey, mchCert<PERSON>ey, mchPrivate<PERSON>ey, mchPublicKey, v3secret, payPublicKey, paySerialNo, mchSerialNo, notifyUrl, orderPrefix } = config.get('wechat-pay')
const mp = config.get('mp')

let _privateKey = ''
let _publicKey = ''
let _payPublicKey = ''
async function getPrivateKey () {
  if (_privateKey) return _privateKey
  _privateKey = await fs.readFile(mchPrivateKey, 'utf8')
  return _privateKey
}
async function getPublicKey () {
  if (_publicKey) return _publicKey
  _publicKey = await fs.readFile(mchPub<PERSON><PERSON><PERSON>, 'utf8')
  return _publicKey
}

async function getPayPublicKey () {
  if (_payPublicKey) return _payPublicKey
  _payPublicKey = await fs.readFile(payPublicKey, 'utf8')
  return _payPublicKey
}

async function getAuthHeader (method, url, body) {
  const nonceStr = Math.random().toString(36).substring(2, 17)
  const timestamp = Math.floor(Date.now() / 1000).toString()
  const str = `${method}\n${url}\n${timestamp}\n${nonceStr}\n${method !== 'GET' ? JSON.stringify(body) : ''}\n`
  const pKey = await getPrivateKey()
  const sign = crypto.createSign('RSA-SHA256')
  sign.update(str)
  const signature = sign.sign(pKey, 'base64')
  return `WECHATPAY2-SHA256-RSA2048 mchid="${mchId}",nonce_str="${nonceStr}",timestamp="${timestamp}",serial_no="${mchSerialNo}",signature="${signature}"`
}

const requestClient = axios.create({
  baseURL: 'https://api.mch.weixin.qq.com',
  headers: { Accept: 'application/json', 'Content-Type': 'application/json' },
  timeout: 20 * 1000
})

/**
 * 微信支付，验证回调签名
 * @param {string} serialNo 平台证书公钥
 * @param {string} signature 签名
 * @param {string} timestamp 时间戳
 * @param {string} nonce 随机字符串
 * @param {object} body 请求体
 * @returns {Promise<boolean>} 是否验证通过
 */
export const verifySignature = async (serialNo, signature, timestamp, nonce, body) => {
  if (serialNo !== paySerialNo) {
    return false
    // throw new GraphQLError('非法请求', { extensions: { code: httpErrorCodes.FORBIDDEN } })
  }
  body = typeof body === 'object' ? JSON.stringify(body) : body
  const str = `${timestamp}\n${nonce}\n${body}\n`
  const pKey = await getPayPublicKey()
  const verify = crypto.createVerify('RSA-SHA256')
  verify.update(str)
  return verify.verify(pKey, signature, 'base64')
}

/**
 * 微信支付解密数据
 * @param {object} encrypted 加密数据
 * @returns {object} 解密后的数据
 */
export function decryptResource (encrypted) {
  const { ciphertext, associated_data: associatedData, nonce } = encrypted
  const encryptedBuffer = Buffer.from(ciphertext, 'base64')
  const authTag = encryptedBuffer.subarray(encryptedBuffer.length - 16)
  const encryptedData = encryptedBuffer.subarray(
    0,
    encryptedBuffer.length - 16
  )
  const decipher = crypto.createDecipheriv('aes-256-gcm', v3secret, nonce)
  decipher.setAuthTag(authTag)
  decipher.setAAD(Buffer.from(associatedData))
  const decrypted = Buffer.concat([
    decipher.update(encryptedData),
    decipher.final()
  ])
  const decryptedString = decrypted.toString('utf8')
  console.log(decryptedString)
  return JSON.parse(decryptedString)
}

export const requestRefund = async (app, info) => {
  console.log('refund')
  console.log(info)
  const url = '/v3/refund/domestic/refunds'
  const method = 'POST'
  const auth = await getAuthHeader(method, url, info)
  try {
    const res = await requestClient({
      headers: { Authorization: auth },
      method,
      url,
      data: info
    })
    console.log(res.data)
    return res.data
  } catch (error) {
    console.error(error.message)
    if (error.response) {
      console.log(error.response.status)
      console.log(error.response.data)
    }
    throw new GraphQLError('微信退款失败', { extensions: { code: httpErrorCodes.CUSTOM } })
  }
}

/**
 * 微信转账 https://pay.weixin.qq.com/doc/v3/merchant/4012458841
 * @param {object} info 转账信息
 * @returns {Promise<object>} 转账结果
 */
export const transferToUser = async (info) => {
  const url = '/v3/transfer/batches'
  const method = 'POST'
  const auth = await getAuthHeader(method, url, info)
  try {
    const res = await requestClient({
      headers: { Authorization: auth, 'Wechatpay-Serial': paySerialNo },
      method,
      url,
      data: info
    })
    console.log(res.data)
    return res.data
  } catch (error) {
    console.error(error.message)
    if (error.response) {
      console.log(error.response.status)
      console.log(error.response.data)
    }
    throw new GraphQLError('微信转账失败', { extensions: { code: httpErrorCodes.CUSTOM } })
  }
}

/**
 * 解冻分账账单 https://pay.weixin.qq.com/doc/v3/merchant/4012526374
 * @param {string} transactionId 微信支付订单号
 * @param {string} outOrderNo 商户分账单号
 * @returns {Promise<object>} 解冻结果
 */
export const unfreezeSharedFunds = async (transactionId, outOrderNo) => {
  const url = '/v3/profitsharing/orders/unfreeze'
  const method = 'POST'
  const info = {
    transaction_id: transactionId,
    out_order_no: outOrderNo,
    description: '解冻全部剩余资金'
  }
  const auth = await getAuthHeader(method, url, info)
  try {
    const res = await requestClient({
      headers: { Authorization: auth },
      method,
      url,
      data: info
    })
    console.log(res.data)
    return res.data
  } catch (error) {
    console.error(error.message)
    if (error.response) {
      console.log(error.response.status)
      console.log(error.response.data)
    }
    throw new GraphQLError('解冻分账账单失败', { extensions: { code: httpErrorCodes.CUSTOM } })
  }
}

export const addProfitSharingReceiver = async (appId, openId) => {
  const url = '/v3/profitsharing/receivers/add'
  const method = 'POST'
  const info = {
    appid: appId,
    type: 'PERSONAL_OPENID',
    account: openId,
    relation_type: 'USER'
  }
  const auth = await getAuthHeader(method, url, info)
  try {
    const res = await requestClient({
      headers: { Authorization: auth, 'Wechatpay-Serial': paySerialNo },
      method,
      url,
      data: info
    })
    console.log(res.data)
    return res.data
  } catch (error) {
    console.error(error.message)
    if (error.response) {
      console.log(error.response.status)
      console.log(error.response.data)
    }
    throw new GraphQLError('添加分账接收方失败', { extensions: { code: httpErrorCodes.CUSTOM } })
  }
}

// https://pay.weixin.qq.com/doc/v3/merchant/**********
export const profitShareOrder = async (appId, transactionId, outOrderNo, receivers, unfreezeUnsplit = true) => {
  const url = '/v3/profitsharing/orders'
  const method = 'POST'
  const info = {
    appid: appId,
    transaction_id: transactionId,
    out_order_no: outOrderNo,
    receivers: receivers.map(i => ({ type: 'PERSONAL_OPENID', account: i.openId, amount: i.amount, description: i.description })),
    unfreeze_unsplit: unfreezeUnsplit
  }
  const auth = await getAuthHeader(method, url, info)
  try {
    const res = await requestClient({
      headers: { Authorization: auth, 'Wechatpay-Serial': paySerialNo },
      method,
      url,
      data: info
    })
    console.log(res.data)
    return humps.camelizeKeys(res.data)
  } catch (error) {
    console.error(error.message)
    if (error.response) {
      console.log(error.response.status)
      console.log(error.response.data)
    }
    throw new GraphQLError('分账账单失败', { extensions: { code: httpErrorCodes.CUSTOM } })
  }
}
