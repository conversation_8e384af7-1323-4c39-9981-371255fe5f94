import axios from 'axios'
import config from 'config'
import redis from '../db/redis.js'

const labels = {
  100: '正常',
  10001: '广告',
  20001: '时政',
  20002: '色情',
  20003: '辱骂',
  20006: '违法犯罪',
  20008: '欺诈',
  20012: '低俗',
  20013: '版权',
  21000: '其他'
}

const mps = config.get('mp')

class MPApi {
  constructor (mp) {
    if (!mps[mp]) throw new Error('没有对应的小程序')
    const { appID, appSecret } = mps[mp]
    this.app = mp
    this.appID = appID
    this.appSecret = appSecret
    this.instance = axios.create({
      timeout: 30 * 1000,
      validateStatus: (status) => status === 200
    })
  }

  async fetchApi (options) {
    try {
      const res = await this.instance(options)
      // console.log(res)
      if (res.data.errcode) {
        console.error(res.data.errcode)
        console.error(res.data.errmsg)
        const error = new Error('微信服务:' + res.errmsg)
        error.errcode = res.data.errcode
        error.errmsg = res.data.errmsg
        throw error
      }
      return res.data
    } catch (error) {
      if (error.response) {
        console.error(error.response.status)
        console.error(error.response.data)
        throw new Error('微信服务返回错误')
      }
      throw new Error('请求微信服务失败')
    }
  }

  async getToken () {
    const token = await redis.get('access_token:' + this.app)
    if (token) return token
    const { access_token: accessToken, expires_in: expiresIn } = await this.fetchApi({ method: 'post', url: 'https://api.weixin.qq.com/cgi-bin/stable_token', data: { appid: this.appID, secret: this.appSecret, grant_type: 'client_credential' } })
    redis.set('access_token:' + this.app, accessToken, 'EX', expiresIn)
    return accessToken
  }

  async code2session (code) {
    const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${this.appID}&secret=${this.appSecret}&js_code=${code}&grant_type=authorization_code`
    const res = await this.fetchApi(url)
    const { unionid: unionId, openid: openId, session_key: sessionKey } = res
    return { openId, unionId, sessionKey }
  }

  async code2mobile (code) {
    const token = await this.getToken()
    const { phone_info: { phoneNumber, countryCode } } = await this.fetchApi({ method: 'post', url: `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${token}`, data: { code } })
    return { mobile: phoneNumber, mobileCountry: countryCode }
  }

  async checkTextRisky (info) {
    const { scene, openId, content, title } = info
    const data = {
      version: 2,
      scene: scene || 4,
      openid: openId,
      content,
      title
    }
    const token = await this.getToken()
    const { result: { suggest, label } } = await this.fetchApi({ method: 'post', url: `https://api.weixin.qq.com/wxa/msg_sec_check?access_token=${token}`, data })
    if (suggest === 'risky') {
      console.error(`text risky, ${label},  content: ${content}`)
      return `内容审核不通过，包含${labels[label]}信息`
    }
    return null
  }

  async sendSubscribeMessage (info) {
    //     43101
    // user refuse to accept the msg rid: 66e14af8-1f4029f8-4af585c7
    // 表示用户拒绝接收消息
    const { openId, templateId, page, data } = info
    const token = await this.getToken()
    await this.fetchApi({ method: 'post', url: `https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=${token}`, data: { touser: openId, template_id: templateId, page, data, miniprogram_state: 'formal' } })
  }
}

export default MPApi
