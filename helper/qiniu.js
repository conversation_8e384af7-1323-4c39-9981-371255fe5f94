import config from 'config'
import qn from 'qiniu'
const { accessKey, secretKey, scope } = config.get('qiniu')
const mac = new qn.auth.digest.Mac(accessKey, secretKey)
const cf = new qn.conf.Config()
const bucketManager = new qn.rs.BucketManager(mac, cf)
const imageHost = config.get('imgHost')
const production = process.env.NODE_ENV === 'production'

export function getUploadToken (prefix) {
  // expires 单位为秒，为上传凭证的有效时间
  const options = {
    scope,
    expires: 1800,
    forceSaveKey: true,
    saveKey: `${prefix}/$(etag)`, // prefix + '/$(etag)',
    mimeLimit: 'image/*',
    returnBody: `{"key": $(key),"hash": $(etag), "fsize":$(fsize), "imageAve": $(imageAve), "imageHost": "${imageHost}"}`
  }
  const putPolicy = new qn.rs.PutPolicy(options)
  return putPolicy.uploadToken(mac)
}

export function getVideoUploadToken (prefix) {
  // expires 单位为秒，为上传凭证的有效时间
  const options = {
    scope: 'tqq-course',
    expires: 1800,
    forceSaveKey: true,
    saveKey: `${production ? '' : 'dev/'}${prefix}/$(etag)`, // prefix + '/$(etag)',
    mimeLimit: 'video/*',
    returnBody: '{"key": $(key),"hash": $(etag), "fsize":$(fsize), "imageAve": $(imageAve), "imageHost": "https://video.tiqingqing.com/"}'
  }
  const putPolicy = new qn.rs.PutPolicy(options)
  return putPolicy.uploadToken(mac)
}

export function isQiniuCallback (req) {
  const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`
  return qn.util.isQiniuCallback(mac, url, null, req.get('Authorization'))
}

export async function checkFileExists (key) {
  const mac = new qn.auth.digest.Mac(accessKey, secretKey)
  const config = new qn.conf.Config()
  config.useHttpsDomain = true
  config.regionsProvider = qn.httpc.Region.fromRegionId('z0')
  const bucketManager = new qn.rs.BucketManager(mac, config)
  const result = await bucketManager
    .stat(scope, key)
  return result.resp.statusCode === 200
}

export async function uploadImage (key, buffer) {
  const options = {
    scope,
    expires: 1800,
    forceSaveKey: true,
    saveKey: key,
    mimeLimit: 'image/*',
    returnBody: '{"key": $(key),"hash": $(etag), "fsize":$(fsize), "imageAve": $(imageAve)}'
  }
  const putPolicy = new qn.rs.PutPolicy(options)
  const token = putPolicy.uploadToken(mac)
  const config = new qn.conf.Config()
  config.regionsProvider = qn.httpc.Region.fromRegionId('z0')
  const putExtra = new qn.form_up.PutExtra()
  const formUploader = new qn.form_up.FormUploader(config)
  const result = await formUploader
    .putStream(token, options.saveKey, buffer, putExtra)
  const { data, resp } = result
  if (resp.statusCode === 200) {
    return data.key
  } else {
    throw new Error('upload failed')
  }
}

export function getAccessUrl (url, expireInSeconds) {
  // const expireInSeconds = 3600 // 有效期 1 小时

  // 5. 获取当前时间戳 + 有效期 = 到期时间戳（e 参数）
  const deadline = Math.floor(Date.now() / 1000) + expireInSeconds

  // 6. 构造原始链接 + e 参数

  // 7. 生成 token 签名（基于 baseUrl）
  // const token = qiniu.util.accessToken(mac, url)
  // qn.util.generateAccessToken()
  const privateDownloadUrl = bucketManager.privateDownloadUrl(
    'https://video.tiqingqing.com',
    url.replace('https://video.tiqingqing.com/', ''),
    deadline
  )
  return privateDownloadUrl
}
