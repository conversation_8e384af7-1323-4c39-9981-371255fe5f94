import { <PERSON>ronJob } from 'cron'
import _ from 'lodash-es'
import config from 'config'
import dayjs from 'dayjs'
import objectSupport from 'dayjs/plugin/objectSupport.js'
import kn from '../db/kn.js'
import Mp from '../helper/mp.js'
import { transferToUser, requestRefund } from '../helper/wechat-pay.js'
const { refundNotifyUrl } = config.get('wechat-pay')
import { hashID } from '../util/hashids.js'
dayjs.extend(objectSupport)

const jobPlan = new CronJob(
  '0 1 0 * * *',
  async () => {
    console.log('plan job start')
    await kn('plan').where('status', 1).where('endDate', '<', kn.raw('current_date')).update({ status: 2 })
  },
  null,
  true,
  'Asia/Shanghai',
  null,
  true
)

const jobMorning = new CronJob(
  '0 0 6-10,18-22 * * *',
  async () => {
    console.log('Morning job start')
    const mps = {
      tqq: new Mp('tqq'),
      shen: new Mp('shen'),
      mayi: new Mp('mayi')
    }
    const hour = dayjs().hour()
    const date = dayjs().date()
    const endDateOfMonth = dayjs().endOf('month').date()
    const day = dayjs().day()

    // 查看当前小时订阅的用户
    const userSubs = await kn('user_subscribe_templates')
      .where('status', 'accept')
      .where('times', '>', 0)
      .whereIn('userOpenId', kn('user').where({ subHour: hour }).select('openId'))
      .select()
    console.log(`user templates length: ${userSubs.length}`)
    for (let i = 0; i < userSubs.length; i++) {
      const userSub = userSubs[i]
      try {
        const user = await kn('user').where('openId', userSub.userOpenId).first()
        if (!user) continue
        // 判断是否在计划执行过程中 是 每天发 不是根据配置来发
        let willSend = false
        let message = '今天也别忘了打卡哦'
        const plan = await kn('plan').where({ userId: user.id, status: 1 }).first()
        if (plan) {
          const dayIndex = dayjs({ hour: 0 }).diff(dayjs(plan.startDate), 'day') + 1
          message = dayIndex <= 0 ? '明天开始减重啦，别忘了打卡哦' : `加油！减重第${dayIndex}/${plan.dates}天，别忘了打卡哦`
          willSend = true
        } else {
          const shenService = await kn('shen_service').where({ userId: user.id }).where('end', '<=', kn.raw('current_date')).first()
          if (shenService) {
            willSend = true
            message = '积优生活进行中！今天也别忘了打卡哦'
          } else {
            const { subFrequency, subDayOrDate } = user
            const status = await kn('status').where({ userId: user.id, deleted: false }).orderBy('id', 'desc').first()
            // const profile = await kn('user_profile').where({ userId: user.id, self: 1 }).first()
            const daysPassed = status ? dayjs().diff(status.createAt, 'day') : 0
            if (subFrequency === 'daily') {
              willSend = true
              message = daysPassed > 7 ? '好久没打卡啦，来打个卡吧' : '今天也别忘了打卡哦'
            } else if (subFrequency === 'weekly' && day === subDayOrDate) {
              willSend = true
              message = daysPassed > 14 ? '好久没打卡啦，来打个卡吧' : '本周也别忘了打开哦'
            } else if (subFrequency === 'monthly' && date === (subDayOrDate < 0 ? endDateOfMonth + subDayOrDate + 1 : subDayOrDate)) {
              willSend = true
              message = daysPassed > 31 ? '好久没打卡啦，来打个卡吧' : '这个月也别忘了打开哦'
            }
          }
        }
        if (!willSend) continue
        console.log(`user: ${user.id}, openid ${user.openId}, message: ${message}`)
        await mps[user.app].sendSubscribeMessage({
          openId: userSub.userOpenId,
          templateId: userSub.templateId,
          page: '/pages/home/<USER>',
          data: {
            thing3: { value: message },
            time2: { value: `${hour.toString().padStart(2, '0')}:00` }
          }
        })
        await kn('user_subscribe_templates').where({ userOpenId: userSub.userOpenId, templateId: userSub.templateId }).decrement('times', 1)
      } catch (error) {
        console.error('Morning job error:', error)
        if (error.errcode === 43101) {
          await kn('user_subscribe_templates').where({ userOpenId: userSub.userOpenId, templateId: userSub.templateId }).update({ status: 'reject', updateAt: kn.raw('CURRENT_TIMESTAMP'), times: 0 })
        }
      }
    }
  },
  null,
  true,
  'Asia/Shanghai',
  null,
  false
)

const jobRemoveOrder = new CronJob(
  '0 */10 * * * *',
  async () => {
    await kn('order')
      .where('status', 0)
      .where('createAt', '<', kn.raw("DATE_SUB(CURRENT_TIMESTAMP, INTERVAL '1 0:10' DAY_MINUTE)"))
      .update({ status: -1, deleteAt: kn.raw('CURRENT_TIMESTAMP') })
  },
  null,
  true,
  'Asia/Shanghai',
  null,
  false
)

const jobRefundShenService = new CronJob(
  '0 0 21 * * 0',
  async () => {
    console.log('Refund shen service job start')
    const allDays = await kn('shen_service_day').where({ status: 2 }).whereNull('transferId').whereNull('refundId').select()
    if (allDays.length === 0) {
      console.log('no waiting payback days')
    }
    console.log(`days length ${allDays.length}`)
    const daysGrouped = _.groupBy(allDays, 'shenServiceId')
    const shenServiceIds = allDays.reduce((memo, item) => memo.indexOf(item.shenServiceId) < 0 ? [...memo, item.shenServiceId] : memo, [])
    const shenServiceList = await kn('shen_service').whereIn('id', shenServiceIds).select()
    console.log(`shenServiceList length ${shenServiceList.length}`)
    for (const shenService of shenServiceList) {
      const days = daysGrouped[shenService.id]
      console.log(`shen service id : ${shenService.id}, days: ${days.length}`)
      const amount = days.length * shenService.paybackEverydayAmount
      let preDay = null
      let dayStr = ''
      for (let i = 0; i < days.length; i++) {
        const d = dayjs(days[i].date)
        // if (!preDay) dayStr += d.format('M月D日')
        const str = preDay && preDay.month() === d.month() ? d.format('D日') : d.format('M月D日')
        if (dayStr.length + str.length > 40 || (dayStr.length + str.length === 30 && i !== days.length - 1)) {
          dayStr += '等'
          break
        } else {
          dayStr += str
        }
        preDay = d
      }
      const reasonOrMark = `沈教授教育打卡退款|${dayStr}`
      let throughRefund = true
      const orderId = shenService.orderId
      if (!orderId) {
        throughRefund = false
      } else {
        const { count: alreadyRefoundCount } = await kn('order_refund').where({ orderId }).whereIn('status', ['SUCCESS', 'PROCESSING']).count({ count: '*' }).first()
        if (alreadyRefoundCount >= 50) throughRefund = false
      }
      if (throughRefund) {
        // 退款
        await kn.transaction(async function (trx) {
          const order = await trx('order').where({ id: orderId }).first()
          const [refundId] = await trx('order_refund').insert({
            orderId,
            amount,
            status: 'PROCESSING',
            createAt: kn.raw('CURRENT_TIMESTAMP')
          })
          const refundNo = `service_${hashID.encode(shenService.id)}_${hashID.encode(refundId)}`
          await trx('order_refund').where({ id: refundId }).update({ refundNo })
          await trx('shen_service_day').whereIn('id', days.map(d => d.id)).update({ refundId })
          const info = {
            transaction_id: order.payTransactionId,
            out_refund_no: refundNo,
            reason: reasonOrMark,
            amount: {
              refund: amount,
              total: order.finalAmount,
              currency: 'CNY'
            },
            notify_url: refundNotifyUrl
          }
          const result = await requestRefund('shen', info)
          const update = {
            status: result.status,
            wechatRefundId: result.refund_id,
            channel: result.channel,
            userReceivedAccount: result.user_received_account
          }
          if (result.status === 'SUCCESS') {
            update.updateAt = dayjs(result.success_time).format('YYYY-MM-DD HH:mm:ss')
          }
          await trx('order_refund').where({ id: refundId }).update(update)
        })
      } else {
        // 打款
        await kn.transaction(async function (trx) {
          const appId = config.get('mp').shen.appID
          const sceneId = '1000'
          const batchName = `沈教授教育打卡退款|${days.length}笔` // `沈教授教育打卡${days.length === 1 ? dayjs(days[0].date).format('YY-MM-DD') : dayjs(days[0].date).format('YY-MM-DD') + '至' + dayjs(days[days.length - 1].date).format('YY-MM-DD')}`
          const batchRemark = batchName

          const [transferId] = await trx('wechat_pay_transfer').insert({
            appId,
            // outBatchNo,
            transferSceneId: sceneId,
            batchName,
            batchRemark,
            totalAmount: amount,
            totalNum: days.length,
            batchStatus: 'WAITING'
          })
          const outBatchNo = `service${hashID.encode([shenService.id, transferId])}`
          await trx('wechat_pay_transfer').where({ id: transferId }).update({ outBatchNo })
          const user = await kn('user').where({ id: shenService.userId }).first()
          const details = days.map(i => ({
            transferId,
            openId: user.openId,
            outDetailNo: 'serviceday' + i.id,
            transferAmount: shenService.paybackEverydayAmount,
            transferRemark: `沈教授教育打卡退款｜${dayjs(i.date).format('YY-MM-DD')}`
          }))
          await trx('wechat_pay_transfer_detail').insert(details)
          await trx('shen_service_day').whereIn('id', days.map(d => d.id)).update({ transferId })
          const transInfo = {
            appid: appId,
            out_batch_no: outBatchNo,
            batch_name: batchName,
            batch_remark: batchRemark,
            total_amount: amount,
            total_num: days.length,
            transfer_scene_id: '1000',
            notify_url: config.get('wechat-pay').transferNotifyUrl,
            transfer_detail_list: days.map(i => ({
              openid: user.openId,
              out_detail_no: 'serviceday' + i.id,
              transfer_amount: shenService.paybackEverydayAmount,
              transfer_remark: `沈教授教育打卡退款｜${dayjs(i.date).format('YY-MM-DD')}`
            }))
          }
          console.log(transInfo)
          const { batch_id: batchId, create_time: createTime, batch_status: batchStatus } = await transferToUser(transInfo)
          await trx('wechat_pay_transfer').where('id', transferId).update({ batchId, createTime, batchStatus })
        })
      }
    }
    console.log('job: refund shen service done')
  },
  null,
  true,
  'Asia/Shanghai',
  null,
  false
)

process.on('SIGINT', () => {
  jobMorning.stop()
  jobPlan.stop()
  jobRemoveOrder.stop()
  jobRefundShenService.stop()
  process.exit(0)
})
