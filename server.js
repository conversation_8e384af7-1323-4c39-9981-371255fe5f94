import fs from 'node:fs'
import { createServer } from 'node:http'
import { ApolloServer } from '@apollo/server'
import { expressMiddleware } from '@apollo/server/express4'
import { ApolloServerPluginDrainHttpServer } from '@apollo/server/plugin/drainHttpServer'
import axios from 'axios'
import config from 'config'
import express from 'express'
import session from 'express-session'
import RedisStore from 'connect-redis'

import { makeExecutableSchema } from '@graphql-tools/schema'
import { mapSchema, getDirective, MapperKind } from '@graphql-tools/utils'
import { GraphQLError, defaultFieldResolver, print } from 'graphql'

import httpErrorCode from './util/http-error-code.js'
import resolvers from './graphql/resolvers/index.js'
import kn from './db/kn.js'
import knOld from './db/kn-old.js'
import redis from './db/redis.js'
import loaders from './db/loaders.js'
import excels from './routes/excels.js'
import qywx from './routes/qywx.js'
// import { createAccount, getAccountByToken, getAccountTokenById, reSaveAccount } from './helper/account.js'

import { hashID } from './util/hashids.js'

const app = express()
const redisStore = new RedisStore({
  client: redis,
  prefix: 'admin_auth:'
})
const sessionConfig = {
  secret: 'tao zi rocks',
  store: redisStore,
  resave: false,
  saveUninitialized: false,
  cookie: { secure: false, maxAge: 1000 * 60 * 60 * 24 * 7 },
  unset: 'destroy'
}
const httpServer = createServer(app)
const typeDefs = fs.readFileSync(new URL('./graphql/schema.graphql', import.meta.url), 'utf-8')
let schema = makeExecutableSchema({ typeDefs: [typeDefs], resolvers })
// Transform the schema by applying directive logic
const directives = [
  (fieldConfig) => {
    const authDirective = getDirective(schema, fieldConfig, 'auth')?.[0]
    if (authDirective) {
      const { roles } = authDirective
      const { resolve = defaultFieldResolver } = fieldConfig
      fieldConfig.resolve = async function (source, args, context, info) {
        if (!context.account) throw new GraphQLError('没有登录', { extensions: { code: httpErrorCode.UNAUTHORIZED } })
        if (roles && roles.indexOf(context.account.role) > 0) throw new GraphQLError('没有权限', { extensions: { code: httpErrorCode.FORBIDDEN } })
        return resolve(source, args, context, info)
      }
      return fieldConfig
    }
  },
  (fieldConfig) => {
    const directive = getDirective(schema, fieldConfig, 'mpRedirect')?.[0]
    if (directive) {
      const { auth } = directive
      fieldConfig.resolve = async function (source, args, context, info) {
        const selections = info.operation.selectionSet.selections
        if (selections.length > 1) throw new GraphQLError('只能发送一个请求', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        const app = context.req.get('app')
        if (!app) throw new GraphQLError('没有指定小程序', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        const account = context.account
        if (!account) throw new GraphQLError('没有登录', { extensions: { code: httpErrorCode.UNAUTHORIZED } })
        if (!account.mpUsers || account.mpUsers.length === 0) throw new GraphQLError('请绑定小程序', { extensions: { code: httpErrorCode.UNAUTHORIZED } })
        const mpUser = account.mpUsers.find(i => i.app === app)
        if (!mpUser) throw new GraphQLError('请绑定小程序', { extensions: { code: httpErrorCode.UNAUTHORIZED } })
        const token = await redis.get(`account_id_token:${mpUser.id}`)
        if (auth && !token) throw new GraphQLError('请打开微信登录小程序' + app, { extensions: { code: httpErrorCode.UNAUTHORIZED } })
        // 开始组装请求
        const name = info.operation.selectionSet.selections[0].name.value
        const idVariables = info.operation.variableDefinitions.filter(def => {
          const type = def.type.type ?? def.type
          return type.kind === 'NamedType' && type.name.value === 'ID'
        }
        ).map(def => def.variable.name.value)
        const variables = idVariables.reduce((acc, cur) => ({ ...acc, [cur]: hashID.encode(args[cur]) }), { ...args })
        const data = JSON.stringify({ query: print(info.operation), variables })
        const res = await axios.post(config.get('mpApiUrl'), data, { headers: { 'Content-Type': 'application/json', App: app, 'App-Token': token || '' }, timeout: 5000 })
        if (res.data.errors) {
          throw new GraphQLError(res.data.errors[0].message, { extensions: { code: httpErrorCode.CUSTOM } })
        }
        return res.data.data[name]
      }
      return fieldConfig
    }
  }
]
schema = directives.reduce((schema, directive) => mapSchema(schema, { [MapperKind.OBJECT_FIELD]: directive }), schema)

// 创建记录处理时间的插件
const requestTimingPlugin = {
  async requestDidStart (initialRequestContext) {
    return {
      async executionDidStart (executionRequestContext) {
        return {
          willResolveField ({ source, args, contextValue, info }) {
            const start = Date.now()
            return (error, result) => {
              const end = Date.now()
              const took = end - start
              if (took > 100) {
                console.log(`[${info.parentType.name}.${info.fieldName}] [${took}ms] ${error ? '[error]' : ''}`)
              }
            }
          }
        }
      }
    }
  }
}

const server = new ApolloServer({
  schema,
  csrfPrevention: true,
  plugins: [ApolloServerPluginDrainHttpServer({ httpServer }), requestTimingPlugin],
  status400ForVariableCoercionErrors: true,
  includeStacktraceInErrorResponses: false, // process.env.NODE_ENV !== 'production',
  formatError (formattedError, err) {
    console.error(err)
    if (!formattedError.extensions) return { message: '服务器出错了', code: httpErrorCode.INTERNAL_SERVER_ERROR }
    if (formattedError.extensions.code === httpErrorCode.INTERNAL_SERVER_ERROR) return { message: '服务器出错了', code: httpErrorCode.INTERNAL_SERVER_ERROR }
    return { message: formattedError.message, code: err.extensions.code }
  }
})
await server.start()

app.disable('x-powered-by')
// app.get('/nWXwONBXrF.txt', function (req, res) { res.send('654e81c18b330b9261ff05cd2e93fd73') })
// app.use(morgan(':remote-addr UserId(:userId) ":method :url" :body :status :response-time ms - :res[content-length]'))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '2mb' }))
app.use(express.text({ type: 'text/*', limit: '2mb' }))
app.use(session(sessionConfig))
app.use('/graphql', expressMiddleware(server, {
  async context ({ req }) {
    let account = null
    if (process.env.NODE_ENV !== 'production') req.session.user = 'TaoXiaoLei'
    if (req.session.user) {
      account = await knOld('manage_user').where({ userId: req.session.user }).first()
      if (account) {
        const mpUsers = await kn('user').where({ mobile: account.mobile })
        account.mpUsers = mpUsers
      }
    }
    return { loaders: loaders(account), req, account }
  }
}))
app.use('/excels', excels)
app.use('/callback/qywx', qywx)
app.get('/WW_verify_tbahR8mChzhSn0B9.txt', function (req, res) { res.send('tbahR8mChzhSn0B9') })

httpServer.on('error', error => console.error(error))
await new Promise(resolve => httpServer.listen({ port: config.get('port') }, resolve))
console.log(`🚀 Server ready at port ${config.get('port')} graphql`)
