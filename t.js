// import { unfreezeSharedFunds } from './helper/wechat-pay.js'
// import kn from './db/kn.js'
// const allDays = await kn('shen_service_day').where({ status: 2 }).whereNull('transferId').whereNull('refundId').select()
// console.log(kn('shen_service_day').where({ status: 2 }).whereNull('transferId').whereNull('refundId').select().toString())
// // console.log(allDays)
// const list = [
//   ['4200002651202506216420457029', 'ORD250621976610'],
//   ['4200002723202506216621260960', 'ORD250621156014'],
//   ['4200002645202506201483063660', 'ORD250620582638'],
//   ['4200002662202506202808305335', 'ORD250620234390'],
//   ['4200002658202506062810358803', 'ORD250606NaN'],
//   ['4200002721202506014413768995', 'pro004020']
// ]
// for (const item of list) {
//   console.log(item)
//   const res = await unfreezeSharedFunds(item[0], item[1])
//   console.log(res)
// }
// console.log('done')
import xml2js from 'xml2js'
import config from 'config'
import { getSignature, decrypt } from '@wecom/crypto'

const t = 'zN234FLt8IL0QttzFxPchAFjlO8YIePx4hnXzpPlgY9aEPVmsdbqNj0hAtjyqUhbWBEJT2CntrtTvFLP5YSwRSXfczhS1ObZv7+0JDelnkbNdHgdwkXpqlqBMFPbxxdz5zuJG4Morl6oP4+DLxSA7QXHYoTMj62XLNu3BAgWOaTmirK7lyQb8Xlvrf53ANy+LYvckF5bVAsbm31eLld6jOB0CtSh63um4NKZGRR5yuZ/8yjQGnJFIs+uMIi3PCtOMBMHygBaOIcZ0VGDRghyvSqsMbPX597yb6ohynz6mZ3Z7sk0zmGiEqxe3Ltl0m2QZebmygf3Af6GOobV4QWgW8c15zHXup4zKFNRXRnhk6kHkQnJmkqSsm2yIfvW2fXtdWhVdew1AVPAOqb6BS+pRyzADSuQyiXJQ4UeUtFyI3lqAmVdfglQltT8N92O6+5vQbVDQzSgVDABZkpQ5f9RLnlRxX93jEJeYrEiDD0t6UnpUVbGTBUCP8fbg1O/pUcgZJhzUivGMinmFDOd0jaVU1145Fb38wjPb/01tcPdFcznMGL3S4KVWdcssBRqlNuLN8MaHN3h85B9Nyr7j5236CRv3Lcs+WYs2UErUpn44JgvQaLAfY93nCB4azBqV1oDO6Pg3OYhnitvSW9tmV9mg4RjOP5gWr1mAkZLcu1sUVzWS6ZFMHwOOsnvsAoR6GKZogqhy81xd2g6iiHy1A0Zmw=='

const x = getSignature('mveKMKMucTz0M', 1752817324, 1753159507, t)
console.log(x)
// const str = '<xml><ToUserName><![CDATA[ww11c4a4452a4bcbff]]></ToUserName><Encrypt><![CDATA[/s9vHZe6Y5NvH5Kkl0VAOIKTCj2DkV8KR9xZhXD3izpcFI+HJ0KPUQOcclCrAdWaawwq9bmm2Nj6+NRTagdTskVV2YM9TwEdFVReMAhhvjteGuxw3v/l9k5I1FDZzbqDzhw82EX1yh/4kBayd49SeP0mcVRaYXotR4/Rzl3tLCv17fUn89HXFDYFQMNxQKiSaVniUo7fpO6f4UmTdjuNDrM7zgCY57y/wofIZqvQMa3jkvKrfAMOe6lUx/lllzsl11bCaBneBOiAtuvGm6ApjE2//wgX4XeSdjOO4Lj+q96TrN4TknyXppU73swo19x+51pAAHf4LamZu4rcxCStaHe+im9JTcH2/FC/mQ68UrXAb3vc28YJpF70IRtXp1hTWGeT4111qk5CVCim+YXJVYKsNXiDOM1dWMrLsNOU96WjqI66/Y39xMq7FzLKnzRBfivYnFb7UgFOz1r01YXvVijEZAWQmjoxl7j9WErc2YyAVFzjMZxFIOTiZ3NGtx04RcQcTOopuV19ntAEr/xbPr2uFRyuIUX/CFr6/Yho7QG+JP/AFF6ByzoI58OQWgRcx1YICfvxUrCQIW3IQV/wsQ==]]></Encrypt><AgentID><![CDATA[2000003]]></AgentID></xml>'
const str = '<xml><ToUserName><![CDATA[ww11c4a4452a4bcbff]]></ToUserName><Encrypt><![CDATA[zN234FLt8IL0QttzFxPchAFjlO8YIePx4hnXzpPlgY9aEPVmsdbqNj0hAtjyqUhbWBEJT2CntrtTvFLP5YSwRSXfczhS1ObZv7+0JDelnkbNdHgdwkXpqlqBMFPbxxdz5zuJG4Morl6oP4+DLxSA7QXHYoTMj62XLNu3BAgWOaTmirK7lyQb8Xlvrf53ANy+LYvckF5bVAsbm31eLld6jOB0CtSh63um4NKZGRR5yuZ/8yjQGnJFIs+uMIi3PCtOMBMHygBaOIcZ0VGDRghyvSqsMbPX597yb6ohynz6mZ3Z7sk0zmGiEqxe3Ltl0m2QZebmygf3Af6GOobV4QWgW8c15zHXup4zKFNRXRnhk6kHkQnJmkqSsm2yIfvW2fXtdWhVdew1AVPAOqb6BS+pRyzADSuQyiXJQ4UeUtFyI3lqAmVdfglQltT8N92O6+5vQbVDQzSgVDABZkpQ5f9RLnlRxX93jEJeYrEiDD0t6UnpUVbGTBUCP8fbg1O/pUcgZJhzUivGMinmFDOd0jaVU1145Fb38wjPb/01tcPdFcznMGL3S4KVWdcssBRqlNuLN8MaHN3h85B9Nyr7j5236CRv3Lcs+WYs2UErUpn44JgvQaLAfY93nCB4azBqV1oDO6Pg3OYhnitvSW9tmV9mg4RjOP5gWr1mAkZLcu1sUVzWS6ZFMHwOOsnvsAoR6GKZogqhy81xd2g6iiHy1A0Zmw==]]></Encrypt><AgentID><![CDATA[2000003]]></AgentID></xml>'

const obj = await xml2js.parseStringPromise(str, { explicitArray: false })
console.log(obj)
const { token, encodingAESKey } = config.get('qywx').get('extContactSecret')
const msg = decrypt(encodingAESKey, obj.xml.Encrypt)

const message = await xml2js.parseStringPromise(msg.message, { explicitArray: false })
console.log(message)

// import { sendWelcomeMessage } from "./helper/qywx/send-messge.js"

// await sendWelcomeMessage()

// {
//   xml: {
//     ToUserName: 'ww11c4a4452a4bcbff',
//     FromUserName: 'sys',
//     CreateTime: '1752817324',
//     MsgType: 'event',
//     Event: 'change_external_contact',
//     ChangeType: 'add_external_contact',
//     UserID: 'TaoXiaoLei',
//     ExternalUserID: 'wmUIXXCQAAKuzixK4FYyykh3X_CFTf_g',
//     WelcomeCode: 'JKFxsMea2FNGWmCzCPWdSh3EukkLTRQjX4mDa907i0s'
//   }
// }
